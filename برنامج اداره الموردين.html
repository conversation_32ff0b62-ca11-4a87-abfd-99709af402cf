<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - جمعية المنقف التعاونية</title>

    <!-- Local CSS Files -->
    <link rel="stylesheet" href="assets/css/tailwind.min.css">

    <!-- Local Font (Tajawal) -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
    </style>

    <!-- SheetJS Library for Excel Support -->
    <script src="assets/js/xlsx.full.min.js"></script>

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
            overflow: hidden;
        }

        .form-container,
        .notification-toast {
            transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
        }

        .backdrop-blur-sm {
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
        }

        #desktop-window {
            height: 90vh;
            width: 95vw;
            max-width: 1400px;
            position: absolute;
            transition: all 0.3s ease-in-out;
        }

        #title-bar {
            cursor: move;
            user-select: none;
        }

        .window-content {
            height: calc(100% - 2.5rem);
        }

        .maximized {
            width: 100vw !important;
            height: 100vh !important;
            top: 0 !important;
            left: 0 !important;
            border-radius: 0;
        }

        .minimized {
            height: 2.5rem !important;
            width: 300px !important;
            bottom: 0 !important;
            top: auto !important;
            left: 20px !important;
        }

        .nav-link.active {
            color: #059669;
            font-weight: 700;
        }

        /* Custom scrollbar for better look inside the window */
        .window-content::-webkit-scrollbar,
        .modal-scroll::-webkit-scrollbar {
            width: 8px;
        }

        .window-content::-webkit-scrollbar-track,
        .modal-scroll::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .window-content::-webkit-scrollbar-thumb,
        .modal-scroll::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .window-content::-webkit-scrollbar-thumb:hover,
        .modal-scroll::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* A4 Paper Styles */
        #minutes-content-wrapper {
            background-color: #e2e8f0;
            /* bg-gray-200 */
        }

        #printable-minutes {
            background: white;
            width: 21cm;
            min-height: 29.7cm;
            padding: 2cm;
            margin: 1rem auto;
            box-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);
        }

        #printable-minutes h3,
        #printable-minutes h4 {
            font-weight: bold;
        }

        #printable-minutes table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        #printable-minutes th,
        #printable-minutes td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: right;
        }

        #printable-minutes th {
            background-color: #f2f2f2;
        }

        #printable-minutes .print-textarea {
            width: 100%;
            border: none;
            background-color: transparent;
            resize: none;
            padding: 0;
            margin: 0;
            font-family: inherit;
            font-size: inherit;
        }

        .highlight-new {
            animation: highlight 2s ease-out;
        }

        @keyframes highlight {
            0% {
                background-color: #d1fae5;
            }

            /* bg-emerald-100 */
            100% {
                background-color: transparent;
            }
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            body * {
                visibility: hidden;
            }

            .print-hidden {
                display: none !important;
            }

            #printable-minutes,
            #printable-minutes * {
                visibility: visible;
            }

            #printable-minutes {
                position: absolute;
                left: 0;
                top: 0;
                margin: 0;
                padding: 1cm;
                width: 100%;
                min-height: auto;
                box-shadow: none;
                border: none;
            }
        }

        /* Enhanced Visual Styles */
        .control-section {
            border-left: 4px solid #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .search-section {
            border-left: 4px solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }

        .form-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .btn-hover-effect {
            transition: all 0.3s ease;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .section-divider {
            border-top: 2px solid #e5e7eb;
            margin: 2rem 0;
            position: relative;
        }

        .section-divider::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, #10b981, #3b82f6);
        }
    </style>
</head>

<body class="flex items-center justify-center min-h-screen">

    <button id="relaunch-btn"
        class="hidden bg-emerald-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg z-50 fixed bottom-10">فتح
        برنامج الإدارة</button>

    <div id="desktop-window"
        class="bg-gray-50 rounded-lg shadow-2xl flex flex-col overflow-hidden border border-gray-300">
        <div id="title-bar"
            class="bg-gray-200 px-4 py-2 flex items-center justify-between border-b border-gray-300 h-10 flex-shrink-0">
            <div class="flex items-center space-x-2 space-x-reverse">
                <div id="close-btn-win" class="w-3 h-3 bg-red-500 rounded-full cursor-pointer hover:bg-red-600"></div>
                <div id="minimize-btn-win"
                    class="w-3 h-3 bg-yellow-500 rounded-full cursor-pointer hover:bg-yellow-600"></div>
                <div id="maximize-btn-win" class="w-3 h-3 bg-green-500 rounded-full cursor-pointer hover:bg-green-600">
                </div>
            </div>
            <span id="window-title" class="text-sm font-medium text-gray-700">برنامج إدارة الموردين - جمعية
                المنقف</span>
            <div class="w-16"></div>
        </div>

        <div class="window-content overflow-y-auto bg-gray-50">
            <header class="bg-white shadow-sm sticky top-0 z-40">
                <div class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <svg class="h-10 w-10 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <h1 class="text-xl font-bold text-gray-800">جمعية المنقف التعاونية</h1>
                    </div>
                    <nav id="main-nav" class="hidden md:flex items-center space-x-6 space-x-reverse text-gray-600">
                        <a href="#" class="nav-link active" data-target="main-view">لوحة التحكم</a>
                        <a href="#" class="nav-link" data-target="committee-section">لجنة المشتريات</a>
                        <a href="#" class="nav-link" data-target="meetings-section">الاجتماعات</a>
                        <a href="#" class="nav-link" data-target="reports-section">التقارير</a>
                    </nav>
                </div>
            </header>

            <main id="main-content">
                <div id="main-view" class="page-section">
                    <section id="login-prompt-section" class="relative bg-emerald-700 text-white py-20 md:py-40">
                        <div class="absolute inset-0 bg-black opacity-30"></div>
                        <div class="container mx-auto px-6 text-center relative z-10">
                            <h2 class="text-3xl md:text-5xl font-bold mb-4">نظام إدارة الموردين</h2>
                            <p class="text-lg md:text-xl max-w-3xl mx-auto mb-8">مرحباً بك. يرجى تسجيل الدخول للوصول إلى
                                لوحة تحكم الموردين.</p>
                            <button id="show-signin-btn"
                                class="bg-amber-500 hover:bg-amber-600 text-white font-bold py-3 px-10 rounded-lg shadow-lg transform hover:scale-105 transition-transform duration-300">تسجيل
                                دخول المسؤول</button>
                        </div>
                    </section>
                    <section id="dashboard-section" class="hidden p-8 md:p-12">
                        <!-- Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
                                <div
                                    class="bg-blue-100 text-blue-600 rounded-full h-12 w-12 flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-gray-600">إجمالي الموردين</h3>
                                    <p id="total-suppliers-stat" class="text-3xl font-bold text-blue-600 mt-1">0</p>
                                </div>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
                                <div
                                    class="bg-yellow-100 text-yellow-600 rounded-full h-12 w-12 flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-gray-600">قيد المراجعة</h3>
                                    <p id="pending-suppliers-stat" class="text-3xl font-bold text-yellow-600 mt-1">0</p>
                                </div>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-md flex items-center">
                                <div
                                    class="bg-green-100 text-green-600 rounded-full h-12 w-12 flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-gray-600">المعتمدين</h3>
                                    <p id="approved-suppliers-stat" class="text-3xl font-bold text-green-600 mt-1">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Controls Section -->
                        <div class="control-section bg-white rounded-lg shadow-md p-6 mb-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-4 border-b pb-2">أدوات التحكم المتقدمة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <!-- Category ComboBox -->
                                <div>
                                    <label for="category-select"
                                        class="block text-sm font-medium text-gray-700 mb-1">تصنيف الموردين</label>
                                    <select id="category-select"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-emerald-500 focus:border-emerald-500 bg-white">
                                        <option value="">جميع التصنيفات</option>
                                        <option value="food">المواد الغذائية</option>
                                        <option value="cleaning">مواد التنظيف</option>
                                        <option value="electronics">الإلكترونيات</option>
                                        <option value="office">المكتبية</option>
                                    </select>
                                </div>

                                <!-- Status Filter -->
                                <div>
                                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">حالة
                                        المورد</label>
                                    <select id="status-filter"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-emerald-500 focus:border-emerald-500 bg-white">
                                        <option value="">جميع الحالات</option>
                                        <option value="معتمد">معتمد</option>
                                        <option value="في انتظار المراجعة">في انتظار المراجعة</option>
                                        <option value="مرفوض">مرفوض</option>
                                    </select>
                                </div>

                                <!-- Quick Actions -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">إجراءات سريعة</label>
                                    <div class="flex space-x-2 space-x-reverse">
                                        <button id="export-excel-btn"
                                            class="btn-hover-effect bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm flex items-center transition-colors">
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                                </path>
                                            </svg>
                                            تصدير
                                        </button>
                                        <button id="import-excel-btn"
                                            class="btn-hover-effect bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm flex items-center transition-colors">
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10">
                                                </path>
                                            </svg>
                                            استيراد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Search Section -->
                        <div class="search-section bg-white rounded-lg shadow-md p-6 mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-bold text-gray-800">البحث المتقدم</h3>
                                <button id="toggle-advanced-search"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                    <span id="search-toggle-text">إظهار البحث المتقدم</span>
                                </button>
                            </div>
                            <div id="advanced-search-form" class="hidden">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <label for="search-company"
                                            class="block text-sm font-medium text-gray-700 mb-1">اسم الشركة</label>
                                        <input type="text" id="search-company"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="ابحث عن اسم الشركة">
                                    </div>
                                    <div>
                                        <label for="search-contact"
                                            class="block text-sm font-medium text-gray-700 mb-1">الشخص المسؤول</label>
                                        <input type="text" id="search-contact"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="ابحث عن اسم المسؤول">
                                    </div>
                                    <div>
                                        <label for="search-email"
                                            class="block text-sm font-medium text-gray-700 mb-1">البريد
                                            الإلكتروني</label>
                                        <input type="email" id="search-email"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="ابحث عن البريد الإلكتروني">
                                    </div>
                                </div>
                                <div class="flex justify-end space-x-2 space-x-reverse mt-4">
                                    <button id="clear-search"
                                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">مسح
                                        البحث</button>
                                    <button id="apply-search"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">تطبيق
                                        البحث</button>
                                </div>
                            </div>
                        </div>

                        <div class="section-divider"></div>

                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                            <div>
                                <h2 class="text-3xl font-bold text-gray-800">قائمة الموردين</h2>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse mt-4 sm:mt-0">
                                <button id="add-supplier-btn"
                                    class="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded-lg flex items-center"><svg
                                        class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>إضافة</button>
                                <button id="admin-login-btn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                        </path>
                                    </svg>دخول المدير</button>
                                <button id="logout-btn"
                                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg">خروج</button>
                            </div>
                        </div>
                        <div class="mb-4"><input type="search" id="search-suppliers"
                                placeholder="ابحث عن مورد بالاسم أو الشركة..."
                                class="w-full p-2 border border-gray-300 rounded-lg"></div>
                        <div class="bg-white rounded-lg shadow overflow-x-auto">
                            <table class="w-full min-w-max">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">اسم الشركة</th>
                                        <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">الشخص المسؤول
                                        </th>
                                        <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">الحالة</th>
                                        <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="suppliers-table-body" class="divide-y divide-gray-200"></tbody>
                            </table>
                        </div>
                    </section>
                </div>
                <div id="committee-section" class="page-section hidden p-8 md:p-12">
                    <div class="flex justify-between items-center mb-8">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">مراجعة لجنة المشتريات</h2>
                            <p class="text-gray-600">الطلبات الجديدة في انتظار قرار اللجنة.</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow overflow-x-auto">
                        <table class="w-full min-w-max">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">اسم الشركة</th>
                                    <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">الشخص المسؤول</th>
                                    <th class="text-right py-3 px-4 font-bold text-sm text-gray-600">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="committee-table-body" class="divide-y divide-gray-200"></tbody>
                        </table>
                    </div>
                </div>
                <div id="meetings-section" class="page-section hidden p-8 md:p-12">
                    <div class="flex justify-between items-center mb-8">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">اجتماعات اللجنة</h2>
                            <p class="text-gray-600">جدولة ومتابعة اجتماعات لجنة المشتريات.</p>
                        </div><button id="schedule-meeting-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center"><svg
                                class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>تحديد موعد اجتماع</button>
                    </div>
                    <div id="meetings-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
                </div>
                <div id="reports-section" class="page-section hidden p-8 md:p-12">
                    <h2 class="text-3xl font-bold text-gray-800 mb-6">التقارير</h2>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <p class="text-gray-700">هنا سيتم عرض تقارير مفصلة عن أداء الموردين، الطلبات، وحالات الاعتماد.
                        </p>
                    </div>
                </div>
            </main>
            <footer class="bg-gray-800 text-white py-8">
                <div class="container mx-auto px-6 text-center">
                    <p>&copy; 2025 جمعية المنقف التعاونية. جميع الحقوق محفوظة.</p>
                    <p class="text-sm text-gray-400 mt-2">تصميم وتطوير الواجهة الأمامية بواسطة Gemini</p>
                </div>
            </footer>
        </div>
    </div>

    <!-- Modals Container -->
    <div id="forms-container" class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden overflow-y-auto">
        <div id="backdrop" class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
        <div id="signin-form"
            class="form-container relative bg-white w-full max-w-md mx-auto rounded-lg shadow-2xl p-8 opacity-0 transform scale-95 hidden">
            <button class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800"><svg class="h-6 w-6"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg></button>
            <div class="text-center mb-6"><svg class="h-12 w-12 text-emerald-600 mx-auto" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <h2 class="text-2xl font-bold text-gray-800 mt-4">تسجيل دخول المسؤول</h2>
                <p class="text-gray-500">أدخل بياناتك للوصول للنظام</p>
            </div>
            <form id="signin-form-element" class="space-y-6">
                <div><label for="signin-email" class="block text-sm font-medium text-gray-700 mb-1">اسم
                        المستخدم</label><input type="text" id="signin-email"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="admin"></div>
                <div><label for="signin-password" class="block text-sm font-medium text-gray-700 mb-1">كلمة
                        المرور</label><input type="password" id="signin-password"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="123"></div>
                <div id="signin-error" class="text-red-500 text-sm text-center h-5"></div>
                <div><button type="submit"
                        class="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-300">تسجيل
                        الدخول</button></div>
            </form>
        </div>
        <div id="add-supplier-modal"
            class="form-container relative bg-white w-full max-w-3xl mx-auto rounded-lg shadow-2xl flex flex-col opacity-0 transform scale-95 hidden"
            style="max-height: 90vh;">
            <div class="p-8 flex-shrink-0"><button
                    class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800"><svg class="h-6 w-6"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg></button>
                <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">إضافة مورد جديد</h2>
            </div>
            <form id="add-supplier-form" class="space-y-4 px-8 pb-8 overflow-y-auto modal-scroll">
                <div><label for="company-name" class="block text-sm font-medium text-gray-700 mb-1">اسم
                        الشركة</label><input type="text" id="company-name"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                <div><label for="contact-person" class="block text-sm font-medium text-gray-700 mb-1">الشخص
                        المسؤول</label><input type="text" id="contact-person"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                <div><label for="supplier-email" class="block text-sm font-medium text-gray-700 mb-1">البريد
                        الإلكتروني</label><input type="email" id="supplier-email"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                <div><label class="block text-lg font-bold text-gray-800 mb-2">المستندات المطلوبة</label>
                    <p class="bg-yellow-100 text-yellow-800 p-3 rounded-lg text-sm mb-4"><strong>ملاحظة هامة:</strong>
                        لن يتم اعتماد المورد في حال وجود أي نقص في المستندات التالية.</p>
                    <div id="documents-checklist" class="space-y-3"></div>
                </div>
                <div class="pt-4"><button type="submit"
                        class="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-4 rounded-lg">حفظ
                        المورد</button></div>
            </form>
        </div>
        <div id="supplier-details-modal"
            class="form-container relative bg-white w-full max-w-2xl mx-auto rounded-lg shadow-2xl flex flex-col opacity-0 transform scale-95 hidden"
            style="max-height: 90vh;">
            <div class="p-8 flex-shrink-0"><button
                    class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800"><svg class="h-6 w-6"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg></button>
                <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">تفاصيل المورد</h2>
            </div>
            <div id="details-content" class="space-y-4 px-8 pb-8 overflow-y-auto modal-scroll"></div>
        </div>
        <div id="decision-modal"
            class="form-container relative bg-white w-full max-w-2xl mx-auto rounded-lg shadow-2xl flex flex-col opacity-0 transform scale-95 hidden"
            style="max-height: 90vh;">
            <div class="p-8 flex-shrink-0"><button
                    class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800"><svg class="h-6 w-6"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg></button>
                <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">قرار لجنة المشتريات</h2>
            </div>
            <div id="decision-content" class="px-8 pb-8 space-y-4 overflow-y-auto modal-scroll"></div>
        </div>
        <div id="schedule-meeting-modal"
            class="form-container relative bg-white w-full max-w-lg mx-auto rounded-lg shadow-2xl p-8 opacity-0 transform scale-95 hidden">
            <button class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800"><svg class="h-6 w-6"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg></button>
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">تحديد موعد اجتماع جديد</h2>
            <form id="schedule-meeting-form" class="space-y-4">
                <div><label for="meeting-title" class="block text-sm font-medium text-gray-700 mb-1">عنوان
                        الاجتماع</label><input type="text" id="meeting-title"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                <div class="grid grid-cols-2 gap-4">
                    <div><label for="meeting-date"
                            class="block text-sm font-medium text-gray-700 mb-1">التاريخ</label><input type="date"
                            id="meeting-date" class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                    <div><label for="meeting-time"
                            class="block text-sm font-medium text-gray-700 mb-1">الوقت</label><input type="time"
                            id="meeting-time" class="w-full px-4 py-2 border border-gray-300 rounded-lg" required></div>
                </div>
                <div><label for="meeting-agenda" class="block text-sm font-medium text-gray-700 mb-1">جدول
                        الأعمال</label><textarea id="meeting-agenda" rows="4"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg"></textarea></div>
                <div class="pt-4"><button type="submit"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg">حفظ
                        الاجتماع</button></div>
            </form>
        </div>
        <div id="meeting-minutes-modal"
            class="form-container relative bg-white w-full max-w-5xl mx-auto rounded-lg shadow-2xl flex flex-col opacity-0 transform scale-95 hidden"
            style="max-height: 90vh;">
            <div class="p-8 flex-shrink-0 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-gray-800 text-center">محضر اجتماع لجنة المشتريات</h2>
                <button class="close-btn text-gray-500 hover:text-gray-800"><svg class="h-6 w-6" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg></button>
            </div>
            <div id="minutes-content-wrapper" class="bg-gray-200 p-8 overflow-y-auto modal-scroll">
                <div id="printable-minutes">
                    <div class="text-center border-b pb-4 mb-4">
                        <h3 class="text-xl font-bold">محضر اجتماع لجنة المشتريات</h3>
                        <p>جمعية المنقف التعاونية</p>
                    </div>
                    <div id="minutes-meta" class="mb-4 text-sm text-gray-600"></div>
                    <div class="space-y-6">
                        <!-- Agenda Section -->
                        <div>
                            <h4 class="text-lg font-bold text-gray-800 mb-2 border-b pb-1">أولًا: جدول الأعمال 📋</h4>
                            <div id="minutes-agenda" class="p-2 bg-gray-50 rounded-md whitespace-pre-wrap"></div>
                        </div>

                        <!-- Decisions Section -->
                        <div>
                            <h4 class="text-lg font-bold text-gray-800 mb-2 border-b pb-1">ثانيًا: القرارات والتوصيات
                            </h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="font-bold block mb-1">1️⃣ التصديق على المحضر السابق:</label>
                                    <textarea id="minutes-approval" class="w-full p-2 border border-gray-200 rounded-md"
                                        rows="2">تم التصديق بالإجماع على محضر الاجتماع السابق دون ملاحظات.</textarea>
                                </div>
                                <div id="minutes-decisions-tables">
                                    <!-- Dynamic tables will be inserted here -->
                                </div>
                                <div>
                                    <label class="font-bold block mb-1">5️⃣ مناقشة عقود شركات البهارات
                                        والاستهلاكي:</label>
                                    <textarea id="minutes-contracts"
                                        class="w-full p-2 border border-gray-200 rounded-md" rows="4">الالتزام بتوريد كافة الأصناف وفق المخزون الاستراتيجي.
الالتزام بالعروض الترويجية الخاصة بالمواسم والمهرجانات.
مراجعة سنوية للأسعار وفق السوق المحلي.</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- New Business Section -->
                        <div>
                            <h4 class="text-lg font-bold text-gray-800 mb-2 border-b pb-1">ثالثًا: ما يستجد من أعمال
                            </h4>
                            <textarea id="minutes-new-business" class="w-full p-2 border border-gray-200 rounded-md"
                                rows="3">تم الاتفاق على متابعة كافة الملاحظات الخاصة بالتوريد والجودة بشكل شهري.
رفع التوصيات النهائية لمجلس الإدارة لاعتمادها.</textarea>
                        </div>

                        <!-- Signature Section -->
                        <div>
                            <h4 class="text-lg font-bold text-gray-800 mb-2 border-b pb-1">وختامًا، التوقيعات:</h4>
                            <table class="w-full text-sm">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الصفة</th>
                                        <th>التوقيع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><textarea class="print-textarea w-full text-center" rows="1"></textarea>
                                        </td>
                                        <td>رئيس اللجنة</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td><textarea class="print-textarea w-full text-center" rows="1"></textarea>
                                        </td>
                                        <td>مقرر اللجنة</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td><textarea class="print-textarea w-full text-center" rows="1"></textarea>
                                        </td>
                                        <td>عضو اللجنة</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td><textarea class="print-textarea w-full text-center" rows="1"></textarea>
                                        </td>
                                        <td>عضو اللجنة</td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-gray-50 border-t flex justify-end space-x-2 space-x-reverse">
                <button id="save-minutes-btn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">حفظ
                    المحضر</button>
                <button id="print-minutes-btn"
                    class="bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700 flex items-center"><svg
                        class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                        </path>
                    </svg>طباعة (Word)</button>
            </div>
            <!-- Import Excel Modal -->
            <div id="import-excel-modal"
                class="form-container relative bg-white w-full max-w-2xl mx-auto rounded-lg shadow-2xl p-8 opacity-0 transform scale-95 hidden">
                <button class="close-btn absolute top-4 left-4 text-gray-500 hover:text-gray-800">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <div class="text-center mb-6">
                    <svg class="h-12 w-12 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                    <h2 class="text-2xl font-bold text-gray-800 mt-4">استيراد بيانات الموردين</h2>
                    <p class="text-gray-500">اختر ملف Excel لاستيراد بيانات الموردين</p>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="excel-file-input" class="block text-sm font-medium text-gray-700 mb-2">اختر ملف
                            Excel</label>
                        <input type="file" id="excel-file-input" accept=".xlsx,.xls"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <div id="file-info" class="hidden mt-2 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                                <span id="file-name"></span>
                                <span id="file-size" class="mr-4"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="font-bold text-blue-800 mb-3 flex items-center">
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            تنسيق الملف المطلوب:
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="font-semibold text-blue-700 mb-2">الأعمدة المطلوبة:</h5>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li class="flex items-center">
                                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                                        العمود الأول: اسم الشركة (مطلوب)
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                                        العمود الثاني: الشخص المسؤول (مطلوب)
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                                        العمود الثالث: البريد الإلكتروني (مطلوب)
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                                        العمود الرابع: التصنيف (اختياري)
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                                        العمود الخامس: ملاحظات (اختياري)
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-semibold text-blue-700 mb-2">ملاحظات مهمة:</h5>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                                    <li>• تنسيق البريد الإلكتروني يجب أن يكون صحيحاً</li>
                                    <li>• لن يتم استيراد الشركات المكررة</li>
                                    <li>• الصفوف الفارغة سيتم تجاهلها</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-bold text-green-800 mb-1">تحميل نموذج Excel جاهز</h4>
                                <p class="text-sm text-green-600">احصل على ملف Excel بالتنسيق الصحيح لتعبئة البيانات</p>
                            </div>
                            <button id="download-template-btn-modal"
                                class="btn-hover-effect bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                                تحميل النموذج
                            </button>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div id="import-progress" class="hidden mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-blue-700">جاري الاستيراد...</span>
                            <span id="import-progress-text" class="text-sm text-blue-600">0%</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div id="import-progress-bar"
                                class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                        <button id="download-template-btn"
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">تحميل نموذج</button>
                        <button id="process-import-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg id="import-spinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            <span id="import-btn-text">استيراد البيانات</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Login Modal -->
            <div id="enhanced-login-modal"
                class="form-container relative bg-white w-full max-w-md mx-auto rounded-lg shadow-2xl p-8 opacity-0 transform scale-95 hidden">
                <div class="text-center mb-8">
                    <div class="bg-emerald-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">تسجيل دخول المدير</h2>
                    <p class="text-gray-600">أدخل بيانات الاعتماد للوصول إلى لوحة الإدارة</p>
                </div>

                <form id="enhanced-login-form" class="space-y-6">
                    <div>
                        <label for="admin-username" class="block text-sm font-medium text-gray-700 mb-2">اسم
                            المستخدم</label>
                        <div class="relative">
                            <input type="text" id="admin-username" required
                                class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                                placeholder="أدخل اسم المستخدم">
                            <svg class="absolute left-3 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <label for="admin-password" class="block text-sm font-medium text-gray-700 mb-2">كلمة
                            المرور</label>
                        <div class="relative">
                            <input type="password" id="admin-password" required
                                class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                                placeholder="أدخل كلمة المرور">
                            <svg class="absolute left-3 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" id="remember-admin"
                                class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                        </label>
                        <button type="button" class="text-sm text-emerald-600 hover:text-emerald-500">نسيت كلمة
                            المرور؟</button>
                    </div>
                    <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                        <button type="button"
                            class="close-btn bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">إلغاء</button>
                        <button type="submit"
                            class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transform hover:scale-105 transition-all">تسجيل
                            الدخول</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Email Notification Toast -->
    <div id="email-toast"
        class="notification-toast fixed bottom-5 right-5 bg-gray-900 text-white py-3 px-5 rounded-lg shadow-lg opacity-0 transform translate-y-10 z-50">
        <p id="email-toast-message"></p>
    </div>

    <script>
        // --- DATA STORE & CONFIG ---
        const requiredDocuments = ['صورة الترخيص التجاري', 'كتاب غرفة تجارة وصناعة', 'صورة اعتماد توقيع', 'صورة البطاقة المدنية', 'بيان جمركي للاصناف', 'صور ترخيص استيراد', 'صورة ترخيص صحي للمنشأة', 'صورة قيد وكالة', 'علامة تجارية للاصناف', 'تعميم اتحاد الجمعيات', 'ترخيص تعبئة للمصنع', 'شهادة الهيئة العامة للأغذية للأصناف'];
        let suppliers = [
            {
                id: 1,
                company: 'شركة الأغذية العصرية',
                contact: 'أحمد المصري',
                email: '<EMAIL>',
                category: 'food',
                status: 'معتمد',
                notes: 'تم الاعتماد بعد مراجعة كافة المستندات.',
                documents: requiredDocuments.map(d => ({ req: d, file: 'sample.pdf' })),
                notificationSent: true
            },
            {
                id: 2,
                company: 'مؤسسة النظافة المثالية',
                contact: 'فاطمة العلي',
                email: '<EMAIL>',
                category: 'cleaning',
                status: 'في انتظار المراجعة',
                notes: '',
                documents: [{ req: 'صورة الترخيص التجاري', file: 'license_2.pdf' }],
                notificationSent: false
            },
            {
                id: 3,
                company: 'مجموعة الإلكترونيات المتقدمة',
                contact: 'خالد السالم',
                email: '<EMAIL>',
                category: 'electronics',
                status: 'مرفوض',
                notes: 'نقص في المستندات المطلوبة.',
                documents: [],
                notificationSent: true
            }
        ];
        let meetings = [{ id: 1, title: 'مراجعة الموردين الجدد - يوليو', date: '2025-07-20', time: '10:00', agenda: '1. التصديق على محضر الاجتماع السابق.\n2. مناقشة عروض شركات الخضار (مزارع وموردين).\n3. اعتماد الشركات لأول مرة (فتح ملف مورد جديد).\n4. مناقشة استكمال الأصناف المقدمة من الشركات المعتمدة.\n5. مناقشة عقود شركات البهارات والاستهلاكي.', minutes: { approval: 'تم التصديق بالإجماع.', vegetableDecisions: [], newSupplierDecisions: [], itemCompletionDecisions: [], contracts: 'الالتزام بتوريد كافة الأصناف.', newBusiness: 'متابعة الملاحظات شهرياً.', signatures: ['', '', '', ''] } }];
        let currentMeetingId = null;

        const decisionTablesConfig = [
            { id: 'vegetableDecisions', title: '2️⃣ مناقشة عروض شركات الخضار:' },
            { id: 'newSupplierDecisions', title: '3️⃣ اعتماد الشركات لأول مرة (فتح ملف مورد جديد):' },
            { id: 'itemCompletionDecisions', title: '4️⃣ استكمال أصناف الشركات المعتمدة:' }
        ];

        // --- DOM ELEMENTS ---
        const formsContainer = document.getElementById('forms-container');
        const backdrop = document.getElementById('backdrop');
        const showSigninBtn = document.getElementById('show-signin-btn');
        const signinForm = document.getElementById('signin-form');
        const addSupplierModal = document.getElementById('add-supplier-modal');
        const supplierDetailsModal = document.getElementById('supplier-details-modal');
        const decisionModal = document.getElementById('decision-modal');
        const scheduleMeetingModal = document.getElementById('schedule-meeting-modal');
        const meetingMinutesModal = document.getElementById('meeting-minutes-modal');
        const detailsContent = document.getElementById('details-content');
        const decisionContent = document.getElementById('decision-content');
        const minutesMeta = document.getElementById('minutes-meta');
        const minutesAgenda = document.getElementById('minutes-agenda');
        const minutesApproval = document.getElementById('minutes-approval');
        const minutesDecisionsTables = document.getElementById('minutes-decisions-tables');
        const minutesContracts = document.getElementById('minutes-contracts');
        const minutesNewBusiness = document.getElementById('minutes-new-business');
        const saveMinutesBtn = document.getElementById('save-minutes-btn');
        const printMinutesBtn = document.getElementById('print-minutes-btn');
        const closeButtons = document.querySelectorAll('.close-btn');
        const windowElement = document.getElementById('desktop-window');
        const titleBar = document.getElementById('title-bar');
        const closeBtnWin = document.getElementById('close-btn-win');
        const minimizeBtnWin = document.getElementById('minimize-btn-win');
        const maximizeBtnWin = document.getElementById('maximize-btn-win');
        const relaunchBtn = document.getElementById('relaunch-btn');
        const mainNav = document.getElementById('main-nav');
        const pageSections = document.querySelectorAll('.page-section');
        const navLinks = document.querySelectorAll('.nav-link');
        const signinFormElement = document.getElementById('signin-form-element');
        const signinEmailInput = document.getElementById('signin-email');
        const signinPasswordInput = document.getElementById('signin-password');
        const signinError = document.getElementById('signin-error');
        const loginPromptSection = document.getElementById('login-prompt-section');
        const dashboardSection = document.getElementById('dashboard-section');
        const logoutBtn = document.getElementById('logout-btn');
        const addSupplierBtn = document.getElementById('add-supplier-btn');
        const addSupplierForm = document.getElementById('add-supplier-form');
        const suppliersTableBody = document.getElementById('suppliers-table-body');
        const committeeTableBody = document.getElementById('committee-table-body');
        const documentsChecklist = document.getElementById('documents-checklist');
        const scheduleMeetingBtn = document.getElementById('schedule-meeting-btn');
        const scheduleMeetingForm = document.getElementById('schedule-meeting-form');
        const meetingsList = document.getElementById('meetings-list');
        const emailToast = document.getElementById('email-toast');
        const emailToastMessage = document.getElementById('email-toast-message');
        const searchInput = document.getElementById('search-suppliers');
        const importExcelModal = document.getElementById('import-excel-modal');
        const excelFileInput = document.getElementById('excel-file-input');
        const exportExcelBtn = document.getElementById('export-excel-btn');
        const importExcelBtn = document.getElementById('import-excel-btn');
        const downloadTemplateBtn = document.getElementById('download-template-btn');
        const downloadTemplateBtnModal = document.getElementById('download-template-btn-modal');
        const processImportBtn = document.getElementById('process-import-btn');
        const importProgress = document.getElementById('import-progress');
        const importProgressBar = document.getElementById('import-progress-bar');
        const importProgressText = document.getElementById('import-progress-text');
        const importSpinner = document.getElementById('import-spinner');
        const importBtnText = document.getElementById('import-btn-text');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const categorySelect = document.getElementById('category-select');
        const statusFilter = document.getElementById('status-filter');
        const toggleAdvancedSearchBtn = document.getElementById('toggle-advanced-search');
        const advancedSearchForm = document.getElementById('advanced-search-form');
        const searchToggleText = document.getElementById('search-toggle-text');
        const searchCompanyInput = document.getElementById('search-company');
        const searchContactInput = document.getElementById('search-contact');
        const searchEmailInput = document.getElementById('search-email');
        const clearSearchBtn = document.getElementById('clear-search');
        const applySearchBtn = document.getElementById('apply-search');
        const adminLoginBtn = document.getElementById('admin-login-btn');
        const enhancedLoginModal = document.getElementById('enhanced-login-modal');
        const enhancedLoginForm = document.getElementById('enhanced-login-form');

        // --- MODAL & FORM LOGIC ---
        const showModal = (modalElement) => { formsContainer.classList.remove('hidden'); modalElement.classList.remove('hidden'); setTimeout(() => { modalElement.classList.remove('opacity-0', 'scale-95'); modalElement.classList.add('opacity-100', 'scale-100'); }, 50); };
        const hideAllModals = () => { const activeModal = formsContainer.querySelector('.form-container:not(.hidden)'); if (activeModal) { activeModal.classList.add('opacity-0', 'scale-95'); setTimeout(() => { formsContainer.classList.add('hidden'); activeModal.classList.add('hidden'); }, 500); } };
        showSigninBtn.addEventListener('click', () => showModal(signinForm));
        addSupplierBtn.addEventListener('click', () => { addSupplierForm.reset(); showModal(addSupplierModal); });
        scheduleMeetingBtn.addEventListener('click', () => { scheduleMeetingForm.reset(); showModal(scheduleMeetingModal); });
        importExcelBtn.addEventListener('click', () => { showModal(importExcelModal); });
        adminLoginBtn.addEventListener('click', () => { enhancedLoginForm.reset(); showModal(enhancedLoginModal); });
        closeButtons.forEach(button => button.addEventListener('click', hideAllModals));
        backdrop.addEventListener('click', hideAllModals);
        document.addEventListener('keydown', (e) => { if (e.key === 'Escape' && !formsContainer.classList.contains('hidden')) hideAllModals(); });

        // --- EXCEL FUNCTIONS ---
        const exportToExcel = () => {
            const filteredSuppliers = getFilteredSuppliers();
            const data = [
                ['اسم الشركة', 'الشخص المسؤول', 'البريد الإلكتروني', 'الحالة', 'التصنيف', 'الملاحظات']
            ];

            filteredSuppliers.forEach(supplier => {
                data.push([
                    supplier.company,
                    supplier.contact,
                    supplier.email,
                    supplier.status,
                    supplier.category || 'غير محدد',
                    supplier.notes || ''
                ]);
            });

            const ws = XLSX.utils.aoa_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'الموردين');

            const fileName = `موردين_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            showToast('تم تصدير البيانات بنجاح!');
        };

        const downloadTemplate = () => {
            const templateData = [
                ['اسم الشركة', 'الشخص المسؤول', 'البريد الإلكتروني', 'التصنيف', 'ملاحظات'],
                ['شركة المثال الأول', 'أحمد محمد', '<EMAIL>', 'مواد غذائية', 'مورد موثوق'],
                ['شركة المثال الثاني', 'فاطمة علي', '<EMAIL>', 'مواد تنظيف', 'أسعار تنافسية'],
                ['شركة المثال الثالث', 'محمد سالم', '<EMAIL>', 'أدوات مكتبية', ''],
                ['', '', '', '', '']
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);

            // Set column widths for better readability
            ws['!cols'] = [
                { wch: 25 }, // اسم الشركة
                { wch: 20 }, // الشخص المسؤول
                { wch: 30 }, // البريد الإلكتروني
                { wch: 15 }, // التصنيف
                { wch: 30 }  // ملاحظات
            ];

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'نموذج الموردين');

            XLSX.writeFile(wb, 'نموذج_استيراد_الموردين.xlsx');
            showToast('تم تحميل النموذج بنجاح! يحتوي على أمثلة للتوضيح');
        };

        const processExcelImport = () => {
            const file = excelFileInput.files[0];
            if (!file) {
                showToast('يرجى اختيار ملف Excel أولاً', 'error');
                return;
            }

            // Check file type
            const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                showToast('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)', 'error');
                return;
            }

            // Show progress UI
            importProgress.classList.remove('hidden');
            importSpinner.classList.remove('hidden');
            importBtnText.textContent = 'جاري الاستيراد...';
            processImportBtn.disabled = true;

            // Reset progress
            importProgressBar.style.width = '0%';
            importProgressText.textContent = '0%';

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    if (workbook.SheetNames.length === 0) {
                        showToast('الملف فارغ أو تالف', 'error');
                        return;
                    }

                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (jsonData.length <= 1) {
                        showToast('الملف لا يحتوي على بيانات للاستيراد', 'error');
                        return;
                    }

                    let importedCount = 0;
                    let skippedCount = 0;
                    const errors = [];
                    const totalRows = jsonData.length - 1; // Exclude header row

                    // Process rows with progress updates
                    const processRows = async () => {
                        for (let i = 1; i < jsonData.length; i++) {
                            const row = jsonData[i];

                            // Update progress
                            const progress = Math.round(((i - 1) / totalRows) * 100);
                            importProgressBar.style.width = `${progress}%`;
                            importProgressText.textContent = `${progress}%`;

                            // Skip empty rows
                            if (!row || row.every(cell => !cell)) {
                                continue;
                            }

                            // Validate required fields
                            if (!row[0] || !row[1] || !row[2]) {
                                skippedCount++;
                                errors.push(`الصف ${i + 1}: بيانات ناقصة (اسم الشركة، جهة الاتصال، البريد الإلكتروني مطلوبة)`);
                                continue;
                            }

                            // Validate email format
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test(row[2])) {
                                skippedCount++;
                                errors.push(`الصف ${i + 1}: تنسيق البريد الإلكتروني غير صحيح`);
                                continue;
                            }

                            // Check for duplicate company names
                            const existingSupplier = suppliers.find(s => s.company.toLowerCase() === row[0].toString().toLowerCase());
                            if (existingSupplier) {
                                skippedCount++;
                                errors.push(`الصف ${i + 1}: الشركة "${row[0]}" موجودة مسبقاً`);
                                continue;
                            }

                            const newSupplier = {
                                id: Date.now() + i,
                                company: row[0].toString().trim(),
                                contact: row[1].toString().trim(),
                                email: row[2].toString().trim().toLowerCase(),
                                category: row[3] ? row[3].toString().trim() : 'غير محدد',
                                status: 'في انتظار المراجعة',
                                notes: row[4] ? row[4].toString().trim() : '',
                                documents: [],
                                notificationSent: false
                            };
                            suppliers.push(newSupplier);
                            importedCount++;

                            // Small delay to show progress
                            if (i % 10 === 0) {
                                await new Promise(resolve => setTimeout(resolve, 50));
                            }
                        }

                        // Complete progress
                        importProgressBar.style.width = '100%';
                        importProgressText.textContent = '100%';
                    };

                    await processRows();

                    // Hide progress and reset UI
                    setTimeout(() => {
                        importProgress.classList.add('hidden');
                        importSpinner.classList.add('hidden');
                        importBtnText.textContent = 'استيراد البيانات';
                        processImportBtn.disabled = false;
                    }, 1000);

                    renderAllTables();
                    hideAllModals();

                    // Show detailed results
                    let message = `تم استيراد ${importedCount} مورد بنجاح!`;
                    if (skippedCount > 0) {
                        message += ` تم تخطي ${skippedCount} صف.`;
                    }
                    showToast(message);

                    // Show errors if any
                    if (errors.length > 0 && errors.length <= 5) {
                        setTimeout(() => {
                            showToast(`أخطاء الاستيراد: ${errors.join(', ')}`, 'warning');
                        }, 2000);
                    } else if (errors.length > 5) {
                        setTimeout(() => {
                            showToast(`تم العثور على ${errors.length} خطأ في الاستيراد. يرجى مراجعة الملف.`, 'warning');
                        }, 2000);
                    }

                } catch (error) {
                    console.error('Import error:', error);
                    showToast('خطأ في قراءة الملف. تأكد من صحة التنسيق.', 'error');

                    // Reset UI on error
                    importProgress.classList.add('hidden');
                    importSpinner.classList.add('hidden');
                    importBtnText.textContent = 'استيراد البيانات';
                    processImportBtn.disabled = false;
                }
            };

            reader.onerror = () => {
                showToast('خطأ في قراءة الملف', 'error');

                // Reset UI on error
                importProgress.classList.add('hidden');
                importSpinner.classList.add('hidden');
                importBtnText.textContent = 'استيراد البيانات';
                processImportBtn.disabled = false;
            };

            reader.readAsArrayBuffer(file);
        };

        // --- FILTERING FUNCTIONS ---
        const getFilteredSuppliers = () => {
            const categoryValue = categorySelect.value;
            const statusValue = statusFilter.value;
            const searchValue = searchInput.value.toLowerCase();
            const companySearch = searchCompanyInput.value.toLowerCase();
            const contactSearch = searchContactInput.value.toLowerCase();
            const emailSearch = searchEmailInput.value.toLowerCase();

            return suppliers.filter(supplier => {
                const matchesCategory = !categoryValue || supplier.category === categoryValue;
                const matchesStatus = !statusValue || supplier.status === statusValue;
                const matchesSearch = !searchValue ||
                    supplier.company.toLowerCase().includes(searchValue) ||
                    supplier.contact.toLowerCase().includes(searchValue);
                const matchesCompany = !companySearch || supplier.company.toLowerCase().includes(companySearch);
                const matchesContact = !contactSearch || supplier.contact.toLowerCase().includes(contactSearch);
                const matchesEmail = !emailSearch || supplier.email.toLowerCase().includes(emailSearch);

                return matchesCategory && matchesStatus && matchesSearch &&
                    matchesCompany && matchesContact && matchesEmail;
            });
        };

        // --- ADVANCED SEARCH FUNCTIONS ---
        const toggleAdvancedSearch = () => {
            const isHidden = advancedSearchForm.classList.contains('hidden');
            if (isHidden) {
                advancedSearchForm.classList.remove('hidden');
                searchToggleText.textContent = 'إخفاء البحث المتقدم';
            } else {
                advancedSearchForm.classList.add('hidden');
                searchToggleText.textContent = 'إظهار البحث المتقدم';
            }
        };

        const clearAdvancedSearch = () => {
            searchCompanyInput.value = '';
            searchContactInput.value = '';
            searchEmailInput.value = '';
            searchInput.value = '';
            categorySelect.value = '';
            statusFilter.value = '';
            renderSuppliersTable();
        };

        const applyAdvancedSearch = () => {
            renderSuppliersTable();
            showToast('تم تطبيق البحث بنجاح!');
        };

        // Event Listeners for Excel functions
        exportExcelBtn.addEventListener('click', exportToExcel);
        downloadTemplateBtn.addEventListener('click', downloadTemplate);
        downloadTemplateBtnModal.addEventListener('click', downloadTemplate);
        processImportBtn.addEventListener('click', processExcelImport);

        // File input change event for preview
        excelFileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = `(${(file.size / 1024).toFixed(1)} KB)`;
                fileInfo.classList.remove('hidden');
            } else {
                fileInfo.classList.add('hidden');
            }
        });

        // Event Listeners for filtering
        categorySelect.addEventListener('change', () => renderSuppliersTable(searchInput.value));
        statusFilter.addEventListener('change', () => renderSuppliersTable(searchInput.value));

        // Event Listeners for advanced search
        toggleAdvancedSearchBtn.addEventListener('click', toggleAdvancedSearch);
        clearSearchBtn.addEventListener('click', clearAdvancedSearch);
        applySearchBtn.addEventListener('click', applyAdvancedSearch);

        // Real-time search for advanced search fields
        [searchCompanyInput, searchContactInput, searchEmailInput].forEach(input => {
            input.addEventListener('input', () => {
                if (input.value.length > 2 || input.value.length === 0) {
                    renderSuppliersTable();
                }
            });
        });

        // --- DYNAMIC CONTENT RENDER ---
        const generateChecklist = () => { documentsChecklist.innerHTML = ''; requiredDocuments.forEach((doc, index) => { documentsChecklist.innerHTML += `<div class="flex items-center justify-between bg-gray-50 p-2 rounded-md"><label for="doc-${index}" class="text-sm font-medium text-gray-700">${index + 1}- ${doc}</label><input type="file" id="doc-${index}" data-requirement="${doc}" class="document-input text-sm file:mr-4 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"></div>`; }); };
        const updateDashboardStats = () => {
            document.getElementById('total-suppliers-stat').textContent = suppliers.length;
            document.getElementById('pending-suppliers-stat').textContent = suppliers.filter(s => s.status === 'في انتظار المراجعة').length;
            document.getElementById('approved-suppliers-stat').textContent = suppliers.filter(s => s.status === 'معتمد').length;
        };
        const renderAllTables = (searchTerm = '') => { renderSuppliersTable(searchTerm); renderCommitteeTable(); updateDashboardStats(); };
        const renderSuppliersTable = (searchTerm = '') => {
            suppliersTableBody.innerHTML = '';
            searchInput.value = searchTerm; // Update search input value
            const filteredSuppliers = getFilteredSuppliers();

            if (filteredSuppliers.length === 0) {
                suppliersTableBody.innerHTML = `<tr><td colspan="4" class="text-center py-10 text-gray-500">${searchTerm ? 'لا يوجد موردين يطابقون بحثك.' : 'لا يوجد موردين لعرضهم.'}</td></tr>`;
                return;
            }
            filteredSuppliers.forEach(supplier => {
                const statusColors = { 'معتمد': 'bg-green-100 text-green-800', 'في انتظار المراجعة': 'bg-yellow-100 text-yellow-800', 'مرفوض': 'bg-red-100 text-red-800' };
                const emailIcon = supplier.notificationSent ? `<svg class="w-4 h-4 text-gray-400 inline-block mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path></svg>` : '';
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.dataset.supplierId = supplier.id;
                row.innerHTML = `<td class="py-3 px-4">${supplier.company}</td><td class="py-3 px-4">${supplier.contact}</td><td class="py-3 px-4"><span class="px-2 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full ${statusColors[supplier.status]}">${emailIcon}${supplier.status}</span></td><td class="py-3 px-4"><button class="text-emerald-600 hover:text-emerald-900 details-btn" data-id="${supplier.id}">تفاصيل</button></td>`;
                suppliersTableBody.appendChild(row);
            });
        };
        const renderCommitteeTable = () => {
            committeeTableBody.innerHTML = '';
            const pendingSuppliers = suppliers.filter(s => s.status === 'في انتظار المراجعة');
            if (pendingSuppliers.length === 0) { committeeTableBody.innerHTML = `<tr><td colspan="3" class="text-center py-10 text-gray-500">لا توجد طلبات جديدة للمراجعة.</td></tr>`; return; }
            pendingSuppliers.forEach(supplier => { committeeTableBody.innerHTML += `<tr class="hover:bg-gray-50"><td class="py-3 px-4">${supplier.company}</td><td class="py-3 px-4">${supplier.contact}</td><td class="py-3 px-4 space-x-2 space-x-reverse"><button class="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 decision-btn" data-id="${supplier.id}">اتخاذ قرار</button></td></tr>`; });
        };
        const renderMeetings = () => {
            meetingsList.innerHTML = '';
            if (meetings.length === 0) { meetingsList.innerHTML = `<p class="text-gray-500 col-span-full text-center">لا توجد اجتماعات مجدولة.</p>`; return; }
            meetings.forEach(meeting => { meetingsList.innerHTML += `<div class="bg-white p-4 rounded-lg shadow flex flex-col"><h3 class="font-bold text-lg text-blue-800">${meeting.title}</h3><p class="text-sm text-gray-600">${new Date(meeting.date).toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })} - ${meeting.time}</p><p class="mt-2 text-sm whitespace-pre-wrap flex-grow">${meeting.agenda}</p><div class="mt-4 border-t pt-2"><button class="text-sm text-blue-600 hover:text-blue-800 font-bold write-minutes-btn" data-id="${meeting.id}">كتابة محضر الاجتماع</button></div></div>`; });
        };
        const renderDecisionTables = (meeting) => {
            minutesDecisionsTables.innerHTML = '';
            decisionTablesConfig.forEach(config => {
                const tableRows = meeting.minutes[config.id] || [];
                let rowsHtml = tableRows.map((row, index) => `
                    <tr data-table-id="${config.id}" data-row-index="${index}">
                        <td><textarea class="print-textarea w-full" rows="1">${row.bookNumber}</textarea></td>
                        <td><textarea class="print-textarea w-full" rows="1">${row.supplierName}</textarea></td>
                        <td><textarea class="print-textarea w-full" rows="1">${row.decision}</textarea></td>
                    </tr>
                `).join('');

                minutesDecisionsTables.innerHTML += `
                    <div class="mt-4">
                        <label class="font-bold block mb-1">${config.title}</label>
                        <table class="w-full text-sm">
                            <thead class="bg-gray-100"><tr><th>رقم الكتاب</th><th>اسم المورد</th><th>قرار اللجنة</th></tr></thead>
                            <tbody id="table-body-${config.id}">${rowsHtml}</tbody>
                        </table>
                        <button type="button" class="add-row-btn text-sm text-blue-600 mt-2 print-hidden" data-table-id="${config.id}">+ إضافة صف</button>
                    </div>
                `;
            });
        };
        addSupplierForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const documentInputs = addSupplierForm.querySelectorAll('.document-input');
            const uploadedDocuments = [];
            documentInputs.forEach(input => { if (input.files.length > 0) { uploadedDocuments.push({ req: input.dataset.requirement, file: input.files[0].name }); } });
            const newSupplier = { id: Date.now(), company: document.getElementById('company-name').value, contact: document.getElementById('contact-person').value, email: document.getElementById('supplier-email').value, status: 'في انتظار المراجعة', notes: '', documents: uploadedDocuments, notificationSent: false };
            suppliers.push(newSupplier);
            renderAllTables();
            const newRow = suppliersTableBody.querySelector(`[data-supplier-id="${newSupplier.id}"]`);
            if (newRow) {
                newRow.classList.add('highlight-new');
            }
            hideAllModals();
        });
        scheduleMeetingForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const newMeeting = { id: Date.now(), title: document.getElementById('meeting-title').value, date: document.getElementById('meeting-date').value, time: document.getElementById('meeting-time').value, agenda: document.getElementById('meeting-agenda').value, minutes: { approval: 'تم التصديق بالإجماع.', vegetableDecisions: [], newSupplierDecisions: [], itemCompletionDecisions: [], contracts: 'الالتزام بتوريد كافة الأصناف.', newBusiness: 'متابعة الملاحظات شهرياً.', signatures: ['', '', '', ''] } };
            meetings.push(newMeeting);
            renderMeetings();
            hideAllModals();
        });
        saveMinutesBtn.addEventListener('click', () => {
            if (currentMeetingId === null) return;
            const meeting = meetings.find(m => m.id === currentMeetingId);
            if (meeting) {
                meeting.minutes.approval = minutesApproval.value;
                decisionTablesConfig.forEach(config => {
                    const rows = [];
                    document.querySelectorAll(`#table-body-${config.id} tr`).forEach(tr => {
                        const textareas = tr.querySelectorAll('textarea');
                        rows.push({ bookNumber: textareas[0].value, supplierName: textareas[1].value, decision: textareas[2].value });
                    });
                    meeting.minutes[config.id] = rows;
                });
                meeting.minutes.contracts = minutesContracts.value;
                meeting.minutes.newBusiness = minutesNewBusiness.value;
                const signatureInputs = meetingMinutesModal.querySelectorAll('#printable-minutes tbody textarea');
                meeting.minutes.signatures = Array.from(signatureInputs).map(input => input.value);
                showToast('تم حفظ المحضر بنجاح!');
            }
        });

        enhancedLoginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const username = document.getElementById('admin-username').value;
            const password = document.getElementById('admin-password').value;

            // Simple authentication (in real app, this would be server-side)
            if (username === 'admin' && password === 'admin123') {
                hideAllModals();
                showToast('تم تسجيل الدخول بنجاح! مرحباً بك في لوحة الإدارة');
                // Could enable additional admin features here
            } else {
                showToast('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        });
        printMinutesBtn.addEventListener('click', () => {
            const filename = 'محضر_اجتماع.doc';
            const preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title><style>body{font-family: 'Arial'; direction: rtl;} table{width: 100%; border-collapse: collapse;} th, td{border: 1px solid #ccc; padding: 8px; text-align: right;} th{background-color: #f2f2f2;}</style></head><body>";
            const postHtml = "</body></html>";

            // Create a clone to modify for printing
            const printableClone = document.getElementById('printable-minutes').cloneNode(true);
            printableClone.querySelectorAll('textarea').forEach(area => {
                const p = document.createElement('p');
                p.style.whiteSpace = 'pre-wrap';
                p.textContent = area.value;
                area.parentNode.replaceChild(p, area);
            });
            printableClone.querySelectorAll('.print-hidden').forEach(el => el.remove());

            const html = preHtml + printableClone.innerHTML + postHtml;
            const blob = new Blob(['\ufeff', html], { type: 'application/msword' });
            const url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);
            const downloadLink = document.createElement("a");
            document.body.appendChild(downloadLink);
            if (navigator.msSaveOrOpenBlob) { navigator.msSaveOrOpenBlob(blob, filename); }
            else { downloadLink.href = url; downloadLink.download = filename; downloadLink.click(); }
            document.body.removeChild(downloadLink);
        });

        // --- EVENT DELEGATION FOR DYNAMIC CONTENT ---
        document.body.addEventListener('click', (e) => {
            const target = e.target;
            const supplierId = parseInt(target.dataset.id);
            const meetingId = parseInt(target.dataset.id);

            if (target.classList.contains('details-btn') && supplierId) {
                const supplier = suppliers.find(s => s.id === supplierId);
                if (supplier) {
                    let attachmentsHtml = requiredDocuments.map(reqDoc => { const foundDoc = supplier.documents.find(d => d.req === reqDoc); return foundDoc ? `<li class="flex justify-between items-center p-2 bg-green-50 rounded-md"><span>${reqDoc}</span><span class="text-green-700 font-mono text-sm">${foundDoc.file}</span></li>` : `<li class="flex justify-between items-center p-2 bg-red-50 rounded-md"><span>${reqDoc}</span><span class="text-red-700 text-sm">لم يتم الرفع</span></li>`; }).join('');
                    const notesHtml = supplier.notes ? `<hr><div><strong class="mb-2 block">ملاحظات اللجنة:</strong><p class="text-gray-600 bg-gray-50 p-2 rounded-md">${supplier.notes}</p></div>` : '';
                    detailsContent.innerHTML = `<div><strong>اسم الشركة:</strong> ${supplier.company}</div><div><strong>الشخص المسؤول:</strong> ${supplier.contact}</div><div><strong>البريد الإلكتروني:</strong> <a href="mailto:${supplier.email}" class="text-blue-600 hover:underline">${supplier.email}</a></div><hr><div><strong class="mb-2 block">حالة المستندات:</strong><ul class="space-y-1">${attachmentsHtml}</ul></div>${notesHtml}`;
                    showModal(supplierDetailsModal);
                }
            } else if (target.classList.contains('decision-btn') && supplierId) {
                const supplier = suppliers.find(s => s.id === supplierId);
                if (supplier) {
                    const allDocsUploaded = requiredDocuments.length === supplier.documents.length;
                    const disabledAttr = allDocsUploaded ? '' : 'disabled title="لا يمكن الاعتماد لوجود نقص في المستندات"';
                    const disabledClass = allDocsUploaded ? 'hover:bg-green-600' : 'bg-gray-400 cursor-not-allowed';
                    const warningMsg = allDocsUploaded ? '' : '<p class="text-sm text-red-600 bg-red-50 p-3 rounded-md"><strong>تنبيه:</strong> لا يمكن اعتماد المورد لأن المستندات غير مكتملة.</p>';
                    decisionContent.innerHTML = `<div><strong>اسم الشركة:</strong> ${supplier.company}</div><hr>${warningMsg}<div><label for="committee-notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات اللجنة</label><textarea id="committee-notes" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg"></textarea></div><div class="flex justify-end space-x-2 space-x-reverse pt-4"><button class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 final-reject-btn" data-id="${supplier.id}">رفض المورد</button><button class="bg-green-500 text-white px-4 py-2 rounded ${disabledClass}" ${disabledAttr} data-id="${supplier.id}">اعتماد المورد</button></div>`;
                    showModal(decisionModal);
                }
            } else if (target.classList.contains('final-reject-btn') && supplierId) {
                const supplier = suppliers.find(s => s.id === supplierId);
                if (supplier) { supplier.status = 'مرفوض'; supplier.notes = decisionModal.querySelector('#committee-notes').value; supplier.notificationSent = true; renderAllTables(); hideAllModals(); showToast(`تم إرسال إشعار بالرفض إلى ${supplier.email}`); }
            } else if (target.closest('.bg-green-500:not(.cursor-not-allowed)')) {
                const supplier = suppliers.find(s => s.id === supplierId);
                if (supplier) { supplier.status = 'معتمد'; supplier.notes = decisionModal.querySelector('#committee-notes').value; supplier.notificationSent = true; renderAllTables(); hideAllModals(); showToast(`تم إرسال إشعار بالاعتماد إلى ${supplier.email}`); }
            } else if (target.classList.contains('write-minutes-btn') && meetingId) {
                const meeting = meetings.find(m => m.id === meetingId);
                if (meeting) {
                    currentMeetingId = meetingId;
                    minutesMeta.innerHTML = `<p><strong>بشأن:</strong> ${meeting.title}</p><p><strong>تاريخ الانعقاد:</strong> ${new Date(meeting.date).toLocaleDateString('ar-EG')} - ${meeting.time}</p>`;
                    minutesAgenda.innerHTML = meeting.agenda;
                    minutesApproval.value = meeting.minutes.approval || 'تم التصديق بالإجماع.';
                    renderDecisionTables(meeting);
                    minutesContracts.value = meeting.minutes.contracts || '';
                    minutesNewBusiness.value = meeting.minutes.newBusiness || '';
                    const signatureInputs = meetingMinutesModal.querySelectorAll('#printable-minutes tbody textarea');
                    signatureInputs.forEach((input, index) => {
                        input.value = meeting.minutes.signatures[index] || '';
                    });
                    showModal(meetingMinutesModal);
                }
            } else if (target.classList.contains('add-row-btn')) {
                const tableId = target.dataset.tableId;
                const tableBody = document.getElementById(`table-body-${tableId}`);
                const newRow = `
                    <tr data-table-id="${tableId}" data-row-index="${tableBody.rows.length}">
                        <td><textarea class="print-textarea w-full" rows="1"></textarea></td>
                        <td><textarea class="print-textarea w-full" rows="1"></textarea></td>
                        <td><textarea class="print-textarea w-full" rows="1"></textarea></td>
                    </tr>
                `;
                tableBody.insertAdjacentHTML('beforeend', newRow);
            }
        });

        // --- LOGIN/LOGOUT & UTILITIES ---
        const showToast = (message, type = 'success') => {
            emailToastMessage.textContent = message;

            // Reset classes
            emailToast.className = 'notification-toast fixed bottom-5 right-5 py-3 px-5 rounded-lg shadow-lg opacity-0 transform translate-y-10 z-50';

            // Add type-specific styling
            if (type === 'error') {
                emailToast.classList.add('bg-red-600', 'text-white');
            } else if (type === 'warning') {
                emailToast.classList.add('bg-yellow-600', 'text-white');
            } else {
                emailToast.classList.add('bg-gray-900', 'text-white');
            }

            emailToast.classList.remove('opacity-0', 'translate-y-10');
            emailToast.classList.add('opacity-100', 'translate-y-0');
            setTimeout(() => {
                emailToast.classList.add('opacity-0', 'translate-y-10');
                emailToast.classList.remove('opacity-100', 'translate-y-0');
            }, 3000);
        };
        const handleLogin = (e) => { e.preventDefault(); const email = signinEmailInput.value.trim(); const password = signinPasswordInput.value.trim(); if (email === 'admin' && password === '123') { signinError.textContent = ''; hideAllModals(); setTimeout(() => { loginPromptSection.classList.add('hidden'); dashboardSection.classList.remove('hidden'); generateChecklist(); renderAllTables(); renderMeetings(); document.querySelector('.nav-link[data-target="main-view"]').click(); }, 500); } else { signinError.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة.'; } };
        const handleLogout = () => { dashboardSection.classList.add('hidden'); loginPromptSection.classList.remove('hidden'); };
        signinFormElement.addEventListener('submit', handleLogin);
        logoutBtn.addEventListener('click', handleLogout);
        searchInput.addEventListener('input', (e) => {
            renderSuppliersTable(e.target.value);
        });


        // --- WINDOWS & NAVIGATION (Unchanged) ---
        let isDragging = false, offsetX, offsetY;
        const onMouseDown = (e) => { if (e.target.id !== 'title-bar' && !e.target.parentElement.id === 'title-bar') return; isDragging = true; offsetX = e.clientX - windowElement.offsetLeft; offsetY = e.clientY - windowElement.offsetTop; document.body.style.cursor = 'move'; };
        const onMouseMove = (e) => { if (isDragging) { windowElement.style.left = `${e.clientX - offsetX}px`; windowElement.style.top = `${e.clientY - offsetY}px`; } };
        const onMouseUp = () => { isDragging = false; document.body.style.cursor = 'default'; };
        titleBar.addEventListener('mousedown', onMouseDown); document.addEventListener('mousemove', onMouseMove); document.addEventListener('mouseup', onMouseUp);
        let lastPosition = {};
        closeBtnWin.addEventListener('click', () => { windowElement.classList.add('hidden'); relaunchBtn.classList.remove('hidden'); });
        relaunchBtn.addEventListener('click', () => { windowElement.classList.remove('hidden'); relaunchBtn.classList.add('hidden'); });
        minimizeBtnWin.addEventListener('click', () => { if (!windowElement.classList.contains('minimized')) { lastPosition = { top: windowElement.style.top, left: windowElement.style.left }; } windowElement.classList.toggle('minimized'); if (!windowElement.classList.contains('minimized')) { windowElement.style.top = lastPosition.top; windowElement.style.left = lastPosition.left; } });
        maximizeBtnWin.addEventListener('click', () => { windowElement.classList.toggle('maximized'); });
        mainNav.addEventListener('click', (e) => { e.preventDefault(); const targetLink = e.target.closest('.nav-link'); if (!targetLink) return; const targetSectionId = targetLink.dataset.target; pageSections.forEach(section => { section.classList.add('hidden'); }); document.getElementById(targetSectionId).classList.remove('hidden'); navLinks.forEach(link => { link.classList.remove('active'); }); targetLink.classList.add('active'); });
    </script>
</body>

</html>