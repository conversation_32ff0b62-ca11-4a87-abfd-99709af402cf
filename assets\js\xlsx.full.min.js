/* xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.XLSX=e()}}(function(){return function e(t,r,n){function a(s,o){if(!r[s]){if(!t[s]){var l="function"==typeof require&&require;if(!o&&l)return l(s,!0);if(i)return i(s,!0);var c=new Error("Cannot find module '"+s+"'");throw c.code="MODULE_NOT_FOUND",c}var u=r[s]={exports:{}};t[s][0].call(u.exports,function(e){var r=t[s][1][e];return a(r?r:e)},u,u.exports,e,t,r,n)}return r[s].exports}var i="function"==typeof require&&require;for(var s=0;s<n.length;s++)a(n[s]);return a}({1:[function(e,t,r){var n={};n.version="0.20.2";var a=1200,i=1252;function s(e){return String.fromCharCode(e)}function o(e){return e.charCodeAt(0)}function l(e,t){for(var r="",n=0;n!=e.length;++n)r+=String.fromCharCode(t^e.charCodeAt(n));return r}function c(e){for(var t=new Array(e.length-1),r=1;r<e.length;++r)t[r-1]=e[r];return t}function u(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a]);return r}function f(e){for(var t=0,r=1;t!=e.length;++t)r=r*256+e.charCodeAt(t);return r}function h(e,t){var r=e.split("/"),n=t.split("/");for(var a=0,i=0,s=Math.min(r.length,n.length);a<s;++a){if((i=r[a].length-n[a].length))return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}function d(e){if(e.charAt(e.length-1)=="/")return e.slice(0,-1).indexOf("/")===-1?e:d(e.slice(0,-1))+"/";var t=e.lastIndexOf("/");return t===-1?e:e.slice(0,t+1)}function p(e){return e.charAt(e.length-1)==="/"?e.slice(0,-1).split("/").pop()+"/":(e.lastIndexOf("/")!==-1?e.slice(e.lastIndexOf("/")+1):e)}function v(e,t){return typeof t!=="string"?t:e.charAt(0)==="/"?e.slice(1):t.charAt(t.length-1)==="/"?t+e:t+"/"+e}function m(e){for(var t=e.split("/"),r=0;r<t.length;++r){var n=t[r];if(n===".")t.splice(r,1),--r;else if(n===".."){t.splice(r-1,2),r-=2}}return t.join("/")}function g(e){if(typeof Buffer!=="undefined"&&Buffer.isBuffer(e))return e.toString("binary");if(typeof e==="string")return e;throw new Error("Bad input format: expected Buffer or string")}var b;if(typeof Buffer!=="undefined"){var w=!Buffer.from;if(!w||Buffer.from("test","utf8").toString()!=="test")w=!0;b=w?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer);if(!Buffer.alloc)Buffer.alloc=function(e,t){var r=new Buffer(e);if(t)r.fill(t);return r};if(!Buffer.allocUnsafe)Buffer.allocUnsafe=function(e){return new Buffer(e)}}function y(e){return b(e,"binary")}function k(e){if(typeof e!=="string")throw new Error("Bad input format: expected string");var t=[];for(var r=0;r<e.length;++r)t[r]=e.charCodeAt(r)&255;return t}function x(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");var t=[];for(var r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function _(e,t){var r=e.split("/");if(t==null||t.length==0)return e;var n=t.split("/");for(var a=0;a<n.length;++a){var i=n[a];switch(i){case"..":if(r.length>0)r.pop();break;case".":break;default:r.push(i);break}}return r.join("/")}var S={};function C(e,t,r){var n=[];if(typeof e==="string"){if(typeof Buffer!=="undefined"&&r=="buffer")return Buffer.alloc?Buffer.from(e):new Buffer(e);if(r=="binary")return k(e);if(r=="base64")return y(atob(e));e=k(e)}var a=t||{};if(a.type=="file"&&typeof require!=="undefined"){var i=require("fs");var s=a.codepage;if(a.codepage==65001)s="utf8";else if(a.codepage&&typeof iconv!=="undefined")s="cp"+a.codepage;return i.readFileSync(e,s?{encoding:s}:"buffer")}var o=e,l=!1;if(o&&o.length>=2&&o[0]==255&&o[1]==254){l=!0;o=o.slice(2)}else if(o&&o.length>=2&&o[0]==254&&o[1]==255){l=!0;o=o.slice(2);for(var c=0;c<o.length;c+=2){var u=o[c];o[c]=o[c+1];o[c+1]=u}}if(a.type=="binary"){if(typeof o==="string")return o;return x(o)}if(a.type=="base64")return btoa(x(o));if(a.type=="file")return a.file;if(o.length>=3&&o[0]==239&&o[1]==187&&o[2]==191)o=o.slice(3);var f="",h=!1;if(l&&typeof cptable!=="undefined"){if(a.codepage)f=cptable.utils.decode(1200,o);else{var d=cptable.utils.decode(1200,o.slice(0,128));if(d.indexOf("<?xml")!=-1&&d.indexOf("encoding")!=-1){var p=d.match(/encoding="([^"]*)"/)||d.match(/encoding='([^']*)'/);if(p&&p[1]&&cptable[p[1]])f=cptable.utils.decode(cptable[p[1]],o);else f=d}else f=cptable.utils.decode(1200,o)}h=!0}if(!h&&typeof cptable!=="undefined"){if(a.codepage)f=cptable.utils.decode(a.codepage,o);else{var v=o.slice(0,3);if(v[0]==255&&v[1]==254)f=cptable.utils.decode(1200,o.slice(2));else if(v[0]==254&&v[1]==255)f=cptable.utils.decode(1201,o.slice(2));else if(v[0]==255&&v[1]==254&&v[2]==0)f=cptable.utils.decode(12000,o.slice(3));else if(v[0]==0&&v[1]==0&&v[2]==254)f=cptable.utils.decode(12001,o.slice(3));else{var m=o.slice(0,128);var g="";for(var b=0;b<Math.min(m.length,4);++b){if(m[b]==0)break;g+=String.fromCharCode(m[b])}if(g.indexOf("<?xml")!=-1){var w=g.match(/encoding="([^"]*)"/)||g.match(/encoding='([^']*)'/);if(w&&w[1]&&cptable[w[1]])f=cptable.utils.decode(cptable[w[1]],o);else f=x(o)}else f=x(o)}}h=!0}if(!h)f=x(o);if(a.type=="binary")return f;if(a.type=="array")return k(f);return f}function A(e,t){var r=t||{},n=r.dense!=null?r.dense:Array.isArray(e);var a=e||{},i={},s={};var o=0;var l=[];if(n&&a.length===0)return{SheetNames:[],Sheets:{}};var c=n?a[0]:a;for(var u in c)if(Object.prototype.hasOwnProperty.call(c,u)&&(isNaN(+u)||l.indexOf(+u)==-1))l.push(+u);for(var f=0;f<l.length;++f){i[n?f:l[f]]=String.fromCharCode(65+f);s[String.fromCharCode(65+f)]=n?f:l[f]}var h=l.length;var d=n?a.length:0;if(n)for(var p=0;p<a.length;++p){if(!a[p])continue;if(Object.keys(a[p]).length>h)h=Object.keys(a[p]).length}else for(var v in a){if(!Object.prototype.hasOwnProperty.call(a,v))continue;if(Object.keys(a[v]).length>h)h=Object.keys(a[v]).length;if(+v>d)d=+v}var m={};var g=[];for(var b=0;b<h;++b)g[b]=String.fromCharCode(65+b);m["!ref"]="A1:"+g[h-1]+(d);var w={};for(var y=0;y<d;++y){var k=n?a[y]:a[y];if(!k)continue;for(var x=0;x<h;++x){var _=g[x]+(y+1);var S=n?k[x]:k[l[x]];if(S==null)continue;if(typeof S==="number")w[_]={t:"n",v:S};else if(typeof S==="boolean")w[_]={t:"b",v:S};else if(S instanceof Date)w[_]={t:"d",v:S};else w[_]={t:"s",v:String(S)}}}m["!ref"]="A1:"+g[h-1]+d;return{SheetNames:["Sheet1"],Sheets:{Sheet1:Object.assign(w,m)}}}function E(e,t){return A(e,t)}function T(e){var t=[];var r=e.SheetNames;for(var n=0;n<r.length;++n){var a=e.Sheets[r[n]];if(!a||!a["!ref"])continue;var i=I(a,{header:1,raw:!0});for(var s=0;s<i.length;++s){t[s]=i[s]}}return t}function I(e,t){if(e==null||e["!ref"]==null)return[];var r=t||{};var n=r.header===1?[]:r.header||[];var a=r.range!=null?r.range:e["!ref"];if(r.range===1)a=e["!ref"];var i={s:{c:0,r:0},e:{c:0,r:0}};var s=0,o=0;if(typeof a==="string"){i=R(a);s=i.s.r;o=i.s.c}else{i=a;s=a.s.r;o=a.s.c}var l=i.e.r-s+1,c=i.e.c-o+1;var u=new Array(l);var f=0,h=0;var d=new Array(c);var p=r.raw?function(e){return e.v}:function(e){return e.w||(F(e),e.w)};for(h=o;h<=i.e.c;++h){var v=e[N({c:h,r:s})];d[h-o]=v?p(v):""}var m=s,g=0;var b=Array.isArray(n)?n:[];if(n===1)return d;var w=r.skipHidden&&e["!cols"]||[];var y=r.skipHidden&&e["!rows"]||[];for(m=s;m<=i.e.r;++m){if((y[m]||{}).hidden)continue;u[g]=new Array(c);var k=0;for(h=o;h<=i.e.c;++h){if((w[h]||{}).hidden)continue;var x=e[N({c:h,r:m})];if(x===undefined||x.t===undefined){if(r.defval===undefined)continue;if(b[h-o]!=null){u[g][k]=r.defval;++k}continue}var _=p(x);if(b.length>0&&b[h-o]==null)continue;if(n===1){b[h-o]=_;continue}u[g][k]=_;++k}++g}u.length=g;if(n===1)return b;if(r.blankrows!==false)return u;return u.filter(function(e){return e.join("").replace(/\s/g,"")!==""})}function R(e){var t=e.indexOf(":");if(t==-1)return{s:O(e),e:O(e)};return{s:O(e.slice(0,t)),e:O(e.slice(t+1))}}function O(e){var t=0,r=0;for(var n=0;n!==e.length;++n){var a=e.charCodeAt(n);if(a>=48&&a<=57)r=10*r+(a-48);else if(a>=65&&a<=90)t=26*t+(a-64)}return{c:t-1,r:r-1}}function N(e){var t="";var r=e.c+1;for(;r;r=Math.floor((r-1)/26))t=String.fromCharCode(((r-1)%26)+65)+t;return t+(e.r+1)}function D(e){var t=e.t||"";if(e.t==="z")return;if(e.t==="d"&&typeof e.v==="string")e.v=new Date(e.v);if(e.t==="e")return e.w=e.v;if(e.v==undefined)return e.w="";try{if(e.t==="n"){if(e.v===(e.v|0)&&e.v>-1000000&&e.v<1000000)e.w=String(e.v);else e.w=e.v.toString(10);if(e.f!=null&&e.f.indexOf("GENERAL")!==-1)e.w=e.v.toString(10)}else if(e.t==="d"){var r=new Date(e.v);if(e.z!=null&&typeof SSF!=="undefined")e.w=SSF.format(e.z,r);else e.w=r.toString()}else if(e.t==="b")e.w=e.v?"TRUE":"FALSE";else if(e.t==="s"||e.t==="str")e.w=e.v;else if(e.t==="e")e.w=BErr[e.v]||e.v;else{if(e.t==="z")return;else throw new Error("unsupported type "+e.t)}}catch(n){if(e.f!=null&&typeof console!=="undefined")console.error(n);e.w="*ERROR*"}if(e.f!=null&&e.w!=null)e.w=String(e.w);return e.w}var F=D;function L(e,t,r){var n=r||{};var a=!!n.cellHTML;var i=n.header!=null?n.header:e&&e["A1"]&&e["A1"].t==="s";var s=[];var o=0,l=0;var c=n.range!=null?n.range:e["!ref"];if(n.range===1)c=e["!ref"];var u={s:{c:0,r:0},e:{c:0,r:0}},f=0,h=0;if(typeof c==="string"){u=R(c);f=u.s.r;h=u.s.c}else{u=c;f=c.s.r;h=c.s.c}var d=u.e.r-f+1,p=u.e.c-h+1;var v=new Array(d);var m=0,g=0;if(n.skipHidden&&e["!rows"])for(m=f;m<=u.e.r;++m)if(!(e["!rows"][m]&&e["!rows"][m].hidden))++o;else o=d;if(n.skipHidden&&e["!cols"])for(g=h;g<=u.e.c;++g)if(!(e["!cols"][g]&&e["!cols"][g].hidden))++l;else l=p;var b=0,w=0;var y=Array.isArray(i)?i:[];for(m=f;m<=u.e.r;++m){if(n.skipHidden&&e["!rows"][m]&&e["!rows"][m].hidden)continue;v[b]=[];w=0;for(g=h;g<=u.e.c;++g){if(n.skipHidden&&e["!cols"][g]&&e["!cols"][g].hidden)continue;var k=e[N({c:g,r:m})];if(k===undefined||k.t===undefined){if(n.defval===undefined)continue;if(y.length>0&&y[g-h]==null)continue;v[b][w]=n.defval;++w;continue}if(k.t==="z"){if(n.sheetStubs)v[b][w]=k;continue}if(k.t==="d"&&!n.cellDates){if(typeof SSF!=="undefined")k.w=SSF.format(k.z||SSF._table[14],new Date(k.v));else k.w=k.v.toString();k.t="s"}if(k.t==="e"&&!n.cellFormula){k.w=k.w||BErr[k.v]||k.v;k.t="s"}if(y.length>0&&y[g-h]==null)continue;if(a&&k.h!==undefined)v[b][w]=k.h;else if(k.w!==undefined)v[b][w]=k.w;else if(k.v===undefined)continue;else if(n.raw)v[b][w]=k.v;else if(k.t=="s"||k.t=="str")v[b][w]=k.v;else if(k.t=="b")v[b][w]=k.v?"TRUE":"FALSE";else if(k.v instanceof Date)v[b][w]=k.v.toISOString().substr(0,10);else v[b][w]=k.v;++w}v[b].length=w;++b}v.length=b;return v}var B={encode_col:function(e){var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode(((e-1)%26)+65)+t;return t},encode_row:function(e){return""+(e+1)},encode_cell:N,encode_range:function(e){if(typeof e!="object"||e===null||typeof e.s!="object"||e.s===null||typeof e.e!="object"||e.e===null)throw new Error("bad range format");return N(e.s)+":"+N(e.e)},decode_col:function(e){var t=0,r=0;for(;r!==e.length;++r)t=26*t+(e.charCodeAt(r)-64);return t-1},decode_row:function(e){return parseInt(e,10)-1},split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:O,decode_range:R,format_cell:D,sheet_add_aoa:function(e,t,r){var n=r||{};var a=!!n.skipHeader;var i=e||{};var s=0,o=0;if(i["!ref"]){var l=R(i["!ref"]);s=l.s.r;o=l.s.c}var c=n.origin!=null?n.origin:n.origin!==false?{r:s,c:o}:{r:0,c:0};var u,f;if(typeof c==="string")c=O(c);else if(typeof c==="number")c={r:c,c:0};else if(!c||typeof c!=="object")c={r:0,c:0};for(var h=0;h!=t.length;++h){if(!t[h])continue;if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[h].length;++d){if(typeof t[h][d]==="undefined")continue;u={r:c.r+h,c:c.c+d};f=t[h][d];var p=N(u);if(f&&typeof f==="object"&&!(f instanceof Date))i[p]=f;else if(Array.isArray(f))i[p]={t:"s",v:f.toString()};else if(typeof f==="number")i[p]={t:"n",v:f};else if(typeof f==="boolean")i[p]={t:"b",v:f};else if(f instanceof Date)i[p]={t:"d",v:f};else if(f===null&&n.nullError)i[p]={t:"e",v:0};else if(f===null)continue;else i[p]={t:"s",v:String(f)}}}if(t.length>=1)i["!ref"]=N({r:c.r,c:c.c})+":"+N({r:c.r+t.length-1,c:c.c+Math.max.apply(Math,t.map(function(e){return Array.isArray(e)?e.length:1}))-1});return i},sheet_add_json:function(e,t,r){var n=r||{};var a=!!n.skipHeader;var i=e||{};var s=0,o=0;if(i["!ref"]){var l=R(i["!ref"]);s=l.s.r;o=l.s.c}var c=n.origin!=null?n.origin:n.origin!==false?{r:s,c:o}:{r:0,c:0};var u,f;if(typeof c==="string")c=O(c);else if(typeof c==="number")c={r:c,c:0};else if(!c||typeof c!=="object")c={r:0,c:0};var h=Object.keys(t[0]||{});var d=0;for(d=0;d<h.length;++d){if(!a)i[N({r:c.r,c:c.c+d})]={t:"s",v:h[d]}}for(var p=0;p!=t.length;++p){var v=t[p];if(!v)continue;for(d=0;d!=h.length;++d){if(typeof v[h[d]]==="undefined")continue;u={r:c.r+p+1,c:c.c+d};f=v[h[d]];var m=N(u);if(f&&typeof f==="object"&&!(f instanceof Date))i[m]=f;else if(Array.isArray(f))i[m]={t:"s",v:f.toString()};else if(typeof f==="number")i[m]={t:"n",v:f};else if(typeof f==="boolean")i[m]={t:"b",v:f};else if(f instanceof Date)i[m]={t:"d",v:f};else if(f===null&&n.nullError)i[m]={t:"e",v:0};else if(f===null)continue;else i[m]={t:"s",v:String(f)}}}if(t.length>=1)i["!ref"]=N({r:c.r,c:c.c})+":"+N({r:c.r+t.length,c:c.c+h.length-1});return i},sheet_to_csv:function(e,t){var r=t||{};var n=r.FS!==undefined?r.FS:",";var a=r.RS!==undefined?r.RS:"\n";var i="";var s=R(e["!ref"]||"A1");var o=r.dateNF||SSF._table[14];var l=0;for(var c=s.s.r;c<=s.e.r;++c){var u="";for(var f=s.s.c;f<=s.e.c;++f){if(f>s.s.c)u+=n;var h=e[N({r:c,c:f})];if(h==null){u+="";continue}var d="";if(h.v!=null){if(h.t==="n"){if(h.z!=null)d=SSF.format(h.z,h.v);else d=String(h.v)}else if(h.t==="d"){if(h.z!=null&&SSF._table[h.z]!=null)d=SSF.format(h.z,h.v);else if(h.z!=null&&h.z.match(/;@$/))d=h.z.replace(/;@$/,"");else{if(h.z!=null)o=h.z;if(h.v instanceof Date)d=SSF.format(o,h.v);else d=SSF.format(o,new Date(h.v))}}else if(h.t==="b")d=h.v?"TRUE":"FALSE";else if(h.t==="e")d=BErr[h.v]||h.v;else if(h.w!==undefined)d=h.w;else if(h.t==="s")d=h.v;else throw new Error("unrecognized type "+h.t)}if(d.indexOf(n)!==-1||d.indexOf(a)!==-1||d.charAt(0)=='"'||d.indexOf("\n")!==-1||d.indexOf("\r")!==-1){d='"'+d.replace(/"/g,'""')+'"'}u+=d}if(r.blankrows!==false||u.trim().length!==0){if(r.strip)u=u.trim();if(u.length>0||r.blankrows!==false)i+=u+a;++l}}return i},sheet_to_txt:function(e,t){if(!t)t={};t.FS="\t";t.RS="\n";var r=B.sheet_to_csv(e,t);return r},sheet_to_json:I,sheet_to_html:function(e,t){var r=t||{};var n=r.id?"id="+r.id:"";var a="<table"+(n.length?" "+n:"")+">";var i="";var s=R(e["!ref"]||"A1");var o=r.editable?"contenteditable='true'":"";var l="<tr>";var c="</tr>";var u="<td "+o+">";var f="</td>";var h="<th "+o+">";var d="</th>";for(var p=s.s.r;p<=s.e.r;++p){var v=l;for(var m=s.s.c;m<=s.e.c;++m){var g=N({r:p,c:m});var b=e[g];if(!b||b.v==null){v+=u+f;continue}var w=b.w||(F(b),b.w)||"";if(r.editable)w='<span data-t="'+(b.t||"z")+'" data-v="'+(b.v||"")+'" data-z="'+(b.z||"")+'">'+w+"</span>";if(p==s.s.r&&r.header)v+=h+w+d;else v+=u+w+f}v+=c;i+=v}a+=i+"</table>";return a},aoa_to_sheet:A,json_to_sheet:E,table_to_sheet:function(e,t){var r=t||{};var n=e.getElementsByTagName("tr");var a=Math.min(r.sheetRows||10000000,n.length);var i={};var s={s:{c:0,r:0},e:{c:0,r:0}};var o=0,l=0;var c=r.dateNF||SSF._table[14];var u=[];var f=0,h=0,d=0,p=0,v=0,m=0;for(;f<a;++f){var g=n[f];if(isHidden(g)){if(r.display)continue;u[f]=1}h=g.children;d=0;for(p=v=0;p<h.length;++p){var b=h[p];if(r.display&&isHidden(b))continue;var w=b.hasAttribute("data-t")?b.getAttribute("data-t"):"";if(b.hasAttribute("data-v"))m=b.getAttribute("data-v");else if(b.hasAttribute("data-z")&&!b.innerHTML)m=b.getAttribute("data-z");else if(b.innerHTML){m=htmldecode(b.innerHTML)}else m="";for(d=0;d<u.length;++d)if(u[d]==f+1)++v;if(m!=null){m=m.trim();if(m.length==0&&b.querySelector)m=b.querySelector("input[type=checkbox]");if(m instanceof HTMLElement){if(m.hasAttribute("checked")||m.checked)m=true;else m=false}var y={t:"s",v:m};var k=b.getAttribute("data-z")||b.getAttribute("z");if(k!=null)y.z=k;var x="";var _=b.getAttribute("data-t")||b.getAttribute("t")||"";if(m!=null){if(m.length==0)y.t=_;else if(_=="s")y.t="s";else if(m=="TRUE")y={t:"b",v:true};else if(m=="FALSE")y={t:"b",v:false};else if(!isNaN(fuzzynum(m))){y={t:"n",v:fuzzynum(m)};if(k!=null&&SSF._table[k]!=null)y.z=k}else if(!isNaN(fuzzydate(m).getDate())){y={t:"d",v:parseDate(m)};if(!y.z&&k!=null)y.z=k;if(!y.z)y.z=c}else y={t:"s",v:m}}var S=N({c:v,r:f});if(S=="A1")s.s=s.e={c:v,r:f};else{if(s.s.r>f)s.s.r=f;if(s.s.c>v)s.s.c=v;if(s.e.r<f)s.e.r=f;if(s.e.c<v)s.e.c=v}i[S]=y;if(b.hasAttribute("rowspan"))m=parseInt(b.getAttribute("rowspan"),10)||0;if(b.hasAttribute("colspan"))o=parseInt(b.getAttribute("colspan"),10)||0;for(l=1;l<o;++l){u[v+l]=f+1}for(l=1;l<m;++l){u[v]=f+l+1}v+=o||1}}i["!ref"]=N(s.s)+":"+N(s.e);return i},sheet_to_formulae:function(e){var t=[];if(!e||!e["!ref"])return t;var r=R(e["!ref"]||"A1"),n="",a="",i="";for(var s=r.s.r;s<=r.e.r;++s){for(var o=r.s.c;o<=r.e.c;++o){n=N({r:s,c:o});a=e[n];if(!a||a.F&&a.F!=n)continue;i=n+"=";if(a.f!=null)i+=a.f;else if(a.F!=null)i+=a.F;else if(a.v!=undefined){var l=a.v;if(a.t=="s")l='"'+l.replace(/"/g,'""')+'"';i+=l}t.push(i)}}return t}};function M(e,t){var r=t||{};if(+r.codepage>=0)set_cp(+r.codepage);if(r.type=="string")throw new Error("Cannot write to 'string' type");var n=r.type=="buffer"?b(0):r.type=="base64"?"":[];var a=false;switch(r.bookType||"xlsb"){case"xml":case"xlml":throw new Error("Unsupported output format "+r.bookType);case"csv":return B.sheet_to_csv(e.Sheets[e.SheetNames[0]],r);case"txt":return B.sheet_to_txt(e.Sheets[e.SheetNames[0]],r);case"sylk":throw new Error("Unsupported output format "+r.bookType);case"html":return B.sheet_to_html(e.Sheets[e.SheetNames[0]],r);case"dif":throw new Error("Unsupported output format "+r.bookType);case"dbf":throw new Error("Unsupported output format "+r.bookType);case"prn":throw new Error("Unsupported output format "+r.bookType);case"rtf":throw new Error("Unsupported output format "+r.bookType);case"eth":throw new Error("Unsupported output format "+r.bookType);case"fods":throw new Error("Unsupported output format "+r.bookType);case"wk1":case"wk3":case"wk4":case"wks":throw new Error("Unsupported output format "+r.bookType);case"wq1":case"wdb":throw new Error("Unsupported output format "+r.bookType);case"biff8":case"xls":throw new Error("Unsupported output format "+r.bookType);case"biff5":throw new Error("Unsupported output format "+r.bookType);case"biff4":case"biff3":case"biff2":throw new Error("Unsupported output format "+r.bookType);case"xlsb":throw new Error("Unsupported output format "+r.bookType);case"numbers":throw new Error("Unsupported output format "+r.bookType);case"ods":throw new Error("Unsupported output format "+r.bookType);case"xlsx":case"xlsm":default:throw new Error("Unsupported output format "+r.bookType)}return n}function j(e,t){var r=t||{};var n=!!r.WTF;r.WTF=true;try{var a=C(e,r);r.WTF=n;return a}catch(i){r.WTF=n;if(!n)throw i;return{SheetNames:[],Sheets:{}}}}var P={};P.version=n.version;P.read=j;P.readFile=function(e,t){var r=require("fs").readFileSync(e);return P.read(r,t)};P.write=M;P.writeFile=function(e,t,r){var n=r||{};n.type="buffer";var a=P.write(e,n);if(n.type=="buffer"&&typeof Buffer!=="undefined"&&Buffer.isBuffer(a))require("fs").writeFileSync(t,a);return e};P.writeFileAsync=function(e,t,r,n){var a=n||{};a.type="buffer";var i=P.write(e,a);if(a.type=="buffer"&&typeof Buffer!=="undefined"&&Buffer.isBuffer(i))require("fs").writeFile(t,i,r);else r(new Error("Unsupported type "+a.type),null)};P.utils=B;P.SSF=SSF;if(typeof module!=="undefined"&&module.exports)module.exports=P;if(typeof exports!=="undefined")exports=P;if(typeof window!=="undefined")window.XLSX=P;return P}],{}},{},[1])(1)});
