<!DOCTYPE html>
<html lang="ar" dir="rtl">
<!-- Chosen Palette: Sky & Gray -->
<!-- Application Structure Plan: تم إعادة تصميم التطبيق ليركز على محرك سير العمل (Workflow Engine). الواجهة الرئيسية الآن هي لوحة تحكم (Dashboard) تستخدم تصميم Kanban Board لعرض المعاملات في مراحل مختلفة (جديدة، قيد الإجراء، بانتظار الاعتماد، مكتملة). الشريط الجانبي يوفر تنقلاً بين لوحة التحكم، المراسلات (صادر/وارد)، والأرشيف الشجري. تم تعديل نموذج البيانات في Firestore ليشمل حقولاً خاصة بسير العمل (workflowState, history). تم إضافة نافذة منبثقة جديدة لعرض تفاصيل المعاملة واتخاذ إجراءات عليها (مثل "إرسال للاعتماد")، مما يحدث حالتها في قاعدة البيانات ويحركها بين الأعمدة في لوحة التحكم. -->
<!-- Visualization & Content Choices: 
- لوحة التحكم (Kanban Board): الهدف: تتبع سير العمل. الطريقة: أعمدة تمثل كل مرحلة من مراحل العمل، وبطاقات تمثل المعاملات. التفاعل: النقر على بطاقة يفتح تفاصيلها وإجراءاتها. التبرير: يوفر نظرة شاملة وفورية على حالة جميع المعاملات ويجعل تتبعها سهلاً. الأداة: HTML/Tailwind + JS.
- سجل تاريخ المعاملة: الهدف: توثيق ومراجعة. الطريقة: قائمة مرتبة زمنياً داخل نافذة تفاصيل المعاملة. التبرير: يوفر شفافية كاملة حول مسار المعاملة. الأداة: HTML/Tailwind + JS.
- شجرة الأرشفة والمراسلات: الهدف: تنظيم وأرشفة. الطريقة: تم الحفاظ على الواجهات السابقة مع تحسينها لتتكامل مع النظام الجديد. التبرير: فصل وظائف الأرشفة عن متابعة سير العمل النشط. الأداة: HTML/Tailwind + JS.
- النوافذ المنبثقة (Modals): الهدف: إدخال البيانات واتخاذ الإجراءات. الطريقة: نماذج منفصلة لإضافة معاملة جديدة، وعرض تفاصيلها، وإضافة مجلد. التبرير: يوفر واجهات مركزة لكل مهمة. الأداة: HTML/Tailwind + JS.
-->
<!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة سير العمل والأرشفة الإلكترونية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: all 0.3s ease;
        }
        .sidebar-item-active {
            background-color: #e0f2fe;
            color: #0c4a6e;
            font-weight: 600;
        }
        .kanban-column {
            min-width: 280px;
        }
        .tree-toggle {
            transition: transform 0.2s;
        }
        .tree-toggle.expanded {
            transform: rotate(90deg);
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div id="loading-overlay" class="fixed inset-0 bg-white/70 backdrop-blur-sm flex justify-center items-center z-50">
        <div class="text-center">
            <svg class="animate-spin h-8 w-8 text-sky-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
            <p class="mt-2 text-gray-600">جاري تحميل النظام...</p>
        </div>
    </div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside class="w-72 bg-white border-l border-gray-200 p-4 flex flex-col">
            <h1 class="text-2xl font-bold text-sky-800 mb-6">نظام سير العمل</h1>
            
            <div class="space-y-2 mb-4">
                 <button id="add-internal-transaction-btn" class="w-full bg-sky-600 text-white font-semibold py-2 px-3 rounded-lg shadow-md hover:bg-sky-700 transition-colors flex items-center justify-center gap-2 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" /></svg>
                    معاملة داخلية (سير عمل)
                </button>
                 <button id="add-incoming-btn" class="w-full bg-green-600 text-white font-semibold py-2 px-3 rounded-lg shadow-md hover:bg-green-700 transition-colors flex items-center justify-center gap-2 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /></svg>
                    تسجيل معاملة واردة
                </button>
            </div>

            <nav class="flex-grow overflow-y-auto">
                <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mb-2">القائمة الرئيسية</h3>
                <ul>
                     <li><a href="#" id="nav-dashboard" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-sky-600" viewBox="0 0 20 20" fill="currentColor"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /></svg>
                        <span>لوحة التحكم</span>
                    </a></li>
                </ul>
                <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mt-4 mb-2">المراسلات</h3>
                <ul>
                    <li><a href="#" id="nav-incoming" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd" /></svg>
                        <span>الوارد</span>
                    </a></li>
                    <li><a href="#" id="nav-outgoing" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M17 3a1 1 0 00-1-1H4a1 1 0 100 2h12a1 1 0 001-1zm-2.293 6.293a1 1 0 00-1.414-1.414L12 9.586V3a1 1 0 10-2 0v6.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3z" clip-rule="evenodd" /></svg>
                        <span>الصادر</span>
                    </a></li>
                </ul>
                <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider mt-4 mb-2">شجرة الأرشفة</h3>
                <div id="archive-tree"></div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 overflow-y-auto">
            <header class="flex justify-between items-center mb-4">
                <div id="breadcrumb" class="text-sm text-gray-500"></div>
            </header>
            <div id="content-view">
                <!-- Content will be rendered here -->
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="add-folder-modal" class="modal-backdrop"></div>
    <div id="add-transaction-modal" class="modal-backdrop"></div>
    <div id="add-incoming-modal" class="modal-backdrop"></div>
    <div id="detail-modal" class="modal-backdrop"></div>
    <div id="rename-folder-modal" class="modal-backdrop"></div>
    <div id="move-item-modal" class="modal-backdrop"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, onSnapshot, addDoc, deleteDoc, doc, updateDoc, query, where, orderBy, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        
        let allDocuments = [];
        let currentFolderId = null;
        let currentView = 'dashboard';
        let currentUser = { name: "الموظف الحالي" }; 
        let expandedFolders = {};

        const loadingOverlay = document.getElementById('loading-overlay');
        const archiveTreeContainer = document.getElementById('archive-tree');
        const contentView = document.getElementById('content-view');
        const breadcrumb = document.getElementById('breadcrumb');

        const folderIcon = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" /></svg>`;
        const fileIcon = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" /></svg>`;
        const workflowStates = {
            'new': { title: 'معاملات جديدة', color: 'bg-blue-200' },
            'in_progress': { title: 'قيد الإجراء', color: 'bg-yellow-200' },
            'pending_approval': { title: 'بانتظار الاعتماد', color: 'bg-orange-200' },
            'completed': { title: 'مكتملة ومؤرشفة', color: 'bg-green-200' }
        };

        function render() {
            updateActiveSidebar();
            breadcrumb.innerHTML = '';
            switch (currentView) {
                case 'dashboard': renderDashboard(); break;
                case 'archive': renderArchiveView(); break;
                case 'incoming': case 'outgoing': renderCorrespondenceView(currentView); break;
            }
        }

        function renderDashboard() {
            let html = `<div class="mb-4"><h2 class="text-2xl font-bold">لوحة التحكم</h2><p class="text-gray-500">تتبع سير العمل للمعاملات عبر الإدارات والأقسام.</p></div><div class="flex gap-4 overflow-x-auto pb-4">`;
            Object.keys(workflowStates).forEach(state => {
                const stateInfo = workflowStates[state];
                const transactionsInState = allDocuments.filter(d => d.type === 'transaction' && d.workflowState === state);
                html += `<div class="kanban-column flex-shrink-0 w-72"><div class="p-2 rounded-lg ${stateInfo.color}"><h3 class="font-bold text-gray-800 p-2">${stateInfo.title} (${transactionsInState.length})</h3><div class="space-y-2 p-1 h-[calc(100vh-200px)] overflow-y-auto">${transactionsInState.map(t => renderTransactionCard(t)).join('')}</div></div></div>`;
            });
            contentView.innerHTML = html + `</div>`;
        }

        function renderTransactionCard(transaction) {
            return `<div data-id="${transaction.id}" class="item-card bg-white rounded-lg p-3 shadow-sm cursor-pointer hover:shadow-md"><p class="font-bold text-gray-800">${transaction.subject}</p><p class="text-sm text-gray-500">من/إلى: ${transaction.party}</p><div class="text-xs text-gray-400 mt-2 flex justify-between"><span>${transaction.referenceNumber || ''}</span><span>${transaction.createdAt.toLocaleDateString('ar-EG')}</span></div></div>`;
        }
        
        function renderArchiveTree() {
            const folders = allDocuments.filter(d => d.type === 'folder');
            const hasChildren = (folderId) => folders.some(f => f.parentId === folderId);

            const buildTree = (parentId) => {
                const children = folders.filter(f => f.parentId === parentId);
                if (children.length === 0) return '';
                let html = `<ul class="pr-4 border-r border-gray-200 ${expandedFolders[parentId] || parentId === null ? '' : 'hidden'}">`;
                children.forEach(folder => {
                    const isActive = currentView === 'archive' && currentFolderId === folder.id;
                    const isExpanded = expandedFolders[folder.id];
                    const canExpand = hasChildren(folder.id);
                    html += `
                        <li class="relative group">
                            <div class="flex items-center gap-1">
                                <span class="w-4">${canExpand ? `<svg xmlns="http://www.w3.org/2000/svg" data-id="${folder.id}" class="h-4 w-4 text-gray-500 tree-toggle ${isExpanded ? 'expanded' : ''}" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>` : ''}</span>
                                <a href="#" data-id="${folder.id}" class="folder-link flex-1 flex items-center gap-2 px-3 py-1.5 rounded-lg ${isActive ? 'sidebar-item-active' : 'hover:bg-gray-100'}">
                                    ${folderIcon}
                                    <span class="truncate">${folder.name}</span>
                                </a>
                                <button data-id="${folder.id}" class="folder-options-btn opacity-0 group-hover:opacity-100 text-gray-500 p-1 rounded-full hover:bg-gray-200">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
                                </button>
                            </div>
                            ${buildTree(folder.id)}
                        </li>`;
                });
                html += '</ul>';
                return html;
            };
            archiveTreeContainer.innerHTML = `<ul class="space-y-1"><li><div class="flex items-center gap-1"><span class="w-4"></span><a href="#" data-id="null" class="folder-link flex-1 flex items-center gap-2 px-3 py-1.5 rounded-lg ${currentView === 'archive' && currentFolderId === null ? 'sidebar-item-active' : 'hover:bg-gray-100'}">${folderIcon}<span>الرئيسية</span></a></div>${buildTree(null)}</li></ul>`;
        }

        function renderArchiveView() {
            updateBreadcrumb();
            const viewTitle = allDocuments.find(d => d.id === currentFolderId)?.name || 'المجلد الرئيسي';
            const itemsToShow = allDocuments.filter(d => d.parentId === currentFolderId);
            
            let contentHTML = `<div class="bg-white p-4 rounded-xl shadow-sm min-h-full"><h2 class="text-2xl font-bold mb-4">${viewTitle}</h2>`;
            if (itemsToShow.length === 0) {
                contentHTML += `<p class="text-gray-500 text-center py-10">هذا المجلد فارغ.</p>`;
            } else {
                contentHTML += `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">`;
                itemsToShow.forEach(item => {
                    const isFolder = item.type === 'folder';
                    const icon = isFolder ? folderIcon : fileIcon;
                    contentHTML += `<div class="bg-gray-50 rounded-lg p-3 border hover:shadow-md flex flex-col justify-between"><a href="#" data-id="${item.id}" class="${isFolder ? 'folder-link' : 'item-card'} flex items-center gap-2 text-gray-700 font-semibold truncate">${icon}<span class="truncate">${item.name || item.subject}</span></a><div class="text-xs text-gray-400 mt-2 flex justify-between items-center"><span>${item.createdAt.toLocaleDateString('ar-EG')}</span><button data-id="${item.id}" class="delete-item-btn text-gray-400 hover:text-red-600 p-1 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd" /></svg></button></div></div>`;
                });
                contentHTML += `</div>`;
            }
            contentView.innerHTML = contentHTML + `</div>`;
        }

        function renderCorrespondenceView(type) {
            const viewTitle = type === 'incoming' ? 'المراسلات الواردة' : 'المراسلات الصادرة';
            const itemsToShow = allDocuments.filter(d => d.type === 'correspondence' && d.correspondenceType === (type === 'incoming' ? 'وارد' : 'صادر'));
            
            let contentHTML = `<div class="bg-white p-4 rounded-xl shadow-sm min-h-full"><h2 class="text-2xl font-bold mb-4">${viewTitle}</h2>`;
            if (itemsToShow.length === 0) {
                contentHTML += `<p class="text-gray-500 text-center py-10">لا توجد مراسلات.</p>`;
            } else {
                contentHTML += `<div class="overflow-x-auto"><table class="w-full text-sm text-right text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-50"><tr><th scope="col" class="px-6 py-3">الموضوع</th><th scope="col" class="px-6 py-3">الجهة</th><th scope="col" class="px-6 py-3">رقم الإشارة</th><th scope="col" class="px-6 py-3">التاريخ</th></tr></thead><tbody>`;
                itemsToShow.forEach(item => {
                    contentHTML += `<tr data-id="${item.id}" class="item-card bg-white border-b hover:bg-gray-50 cursor-pointer"><th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">${item.subject}</th><td class="px-6 py-4">${item.party}</td><td class="px-6 py-4">${item.referenceNumber || ''}</td><td class="px-6 py-4">${item.date}</td></tr>`;
                });
                contentHTML += `</tbody></table></div>`;
            }
            contentView.innerHTML = contentHTML + `</div>`;
        }

        function updateBreadcrumb() {
            if (currentView !== 'archive') { breadcrumb.innerHTML = ''; return; }
            let path = [];
            let folderId = currentFolderId;
            while(folderId) {
                const folder = allDocuments.find(d => d.id === folderId);
                if (folder) {
                    path.unshift({id: folder.id, name: folder.name});
                    folderId = folder.parentId;
                } else break;
            }
            let html = `<a href="#" class="folder-link text-gray-500 hover:text-sky-600" data-id="null">الرئيسية</a>`;
            path.forEach(p => { html += ` <span class="mx-1">/</span> <a href="#" class="folder-link text-gray-500 hover:text-sky-600" data-id="${p.id}">${p.name}</a>`; });
            breadcrumb.innerHTML = html;
        }
        
        function updateActiveSidebar() {
            document.querySelectorAll('.sidebar-item-active').forEach(el => el.classList.remove('sidebar-item-active'));
            const linkIdMap = { dashboard: 'nav-dashboard', incoming: 'nav-incoming', outgoing: 'nav-outgoing' };
            if (linkIdMap[currentView]) {
                document.getElementById(linkIdMap[currentView]).classList.add('sidebar-item-active');
            } else if (currentView === 'archive') {
                const link = document.querySelector(`.folder-link[data-id="${currentFolderId}"]`);
                if (link) link.classList.add('sidebar-item-active');
            }
        }

        function setupModal(modalContainerId, openBtnId, formId, submitHandler, renderFn) {
            const modalContainer = document.getElementById(modalContainerId);
            const openBtn = document.getElementById(openBtnId);
            modalContainer.innerHTML = renderFn();
            const modalContent = modalContainer.querySelector('.modal-content');
            const form = document.getElementById(formId);
            const open = () => { modalContainer.classList.remove('pointer-events-none', 'opacity-0'); modalContent.classList.remove('-translate-y-10', 'scale-95'); };
            const close = () => { modalContainer.classList.add('opacity-0'); modalContent.classList.add('-translate-y-10', 'scale-95'); setTimeout(() => modalContainer.classList.add('pointer-events-none'), 300); };
            if(openBtn) openBtn.addEventListener('click', open);
            modalContainer.querySelectorAll('.close-modal-btn').forEach(btn => btn.addEventListener('click', close));
            modalContainer.addEventListener('click', (e) => { if (e.target === modalContainer) close(); });
            if (form) { form.addEventListener('submit', async (e) => { e.preventDefault(); await submitHandler(form); form.reset(); close(); }); }
            return { open, close };
        }

        const addFolderModal = setupModal('add-folder-modal', null, 'add-folder-form', handleAddFolder, () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-md transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">مجلد جديد</h3><button class="close-modal-btn text-gray-500">&times;</button></div><form id="add-folder-form" class="p-6 space-y-4"><div><label for="folder-name" class="block text-sm font-medium text-gray-700">اسم المجلد</label><input type="text" id="folder-name" required class="mt-1 block w-full input-field"></div><div class="flex justify-end pt-4"><button type="submit" class="bg-sky-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-sky-700">حفظ</button></div></form></div>`.replaceAll('input-field', 'px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500'));
        const addTransactionModal = setupModal('add-transaction-modal', 'add-internal-transaction-btn', 'add-transaction-form', handleAddTransaction, () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-2xl transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">معاملة داخلية جديدة</h3><button class="close-modal-btn text-gray-500">&times;</button></div><form id="add-transaction-form" class="p-6 space-y-4"><div><label for="trans-subject" class="block text-sm font-medium text-gray-700">الموضوع</label><input type="text" id="trans-subject" required class="mt-1 block w-full input-field"></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label for="trans-party" class="block text-sm font-medium text-gray-700">القسم/الجهة المسؤولة</label><input type="text" id="trans-party" required class="mt-1 block w-full input-field"></div><div><label for="trans-ref" class="block text-sm font-medium text-gray-700">رقم الإشارة (إن وجد)</label><input type="text" id="trans-ref" class="mt-1 block w-full input-field"></div></div><div class="flex justify-end pt-4"><button type="submit" class="bg-sky-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-sky-700">بدء المعاملة</button></div></form></div>`.replaceAll('input-field', 'px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500'));
        const addIncomingModal = setupModal('add-incoming-modal', 'add-incoming-btn', 'add-incoming-form', handleAddIncoming, () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-2xl transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">تسجيل معاملة واردة</h3><button class="close-modal-btn text-gray-500">&times;</button></div><form id="add-incoming-form" class="p-6 space-y-4"><div><label for="inc-subject" class="block text-sm font-medium text-gray-700">الموضوع</label><input type="text" id="inc-subject" required class="mt-1 block w-full input-field"></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label for="inc-party" class="block text-sm font-medium text-gray-700">الجهة الوارد منها</label><input type="text" id="inc-party" required class="mt-1 block w-full input-field"></div><div><label for="inc-ref" class="block text-sm font-medium text-gray-700">رقم الإشارة</label><input type="text" id="inc-ref" class="mt-1 block w-full input-field"></div></div><div><label for="inc-date" class="block text-sm font-medium text-gray-700">تاريخ الورود</label><input type="date" id="inc-date" required class="mt-1 block w-full input-field"></div><div class="flex justify-end pt-4"><button type="submit" class="bg-green-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700">حفظ الوارد</button></div></form></div>`.replaceAll('input-field', 'px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500'));
        
        async function handleAddFolder(form) {
            await addDoc(collection(db, `artifacts/${appId}/public/data/documents`), { name: form.querySelector('#folder-name').value, type: 'folder', parentId: form.dataset.parentId === 'null' ? null : form.dataset.parentId, createdAt: serverTimestamp() });
        }
        async function handleRenameFolder(form) {
            await updateDoc(doc(db, `artifacts/${appId}/public/data/documents`, form.dataset.folderId), { name: form.querySelector('#new-folder-name').value });
        }
        async function handleAddTransaction(form) {
            await addDoc(collection(db, `artifacts/${appId}/public/data/documents`), { type: 'transaction', subject: form.querySelector('#trans-subject').value, party: form.querySelector('#trans-party').value, referenceNumber: form.querySelector('#trans-ref').value, createdAt: serverTimestamp(), workflowState: 'new', history: [{ action: 'إنشاء المعاملة', user: currentUser.name, timestamp: new Date() }] });
        }
        async function handleAddIncoming(form) {
             await addDoc(collection(db, `artifacts/${appId}/public/data/documents`), { type: 'correspondence', correspondenceType: 'وارد', subject: form.querySelector('#inc-subject').value, party: form.querySelector('#inc-party').value, referenceNumber: form.querySelector('#inc-ref').value, date: form.querySelector('#inc-date').value, createdAt: serverTimestamp() });
        }
        async function handleTakeAction(docId, action) {
            const docRef = doc(db, `artifacts/${appId}/public/data/documents`, docId);
            const transaction = allDocuments.find(d => d.id === docId);
            let newState = transaction.workflowState;
            let historyMessage = '';
            switch(action) {
                case 'start_progress': newState = 'in_progress'; historyMessage = 'بدء الإجراء على المعاملة'; break;
                case 'send_for_approval': newState = 'pending_approval'; historyMessage = 'إرسال للاعتماد'; break;
                case 'approve': newState = 'completed'; historyMessage = 'تم الاعتماد النهائي'; break;
            }
            const newHistoryEntry = { action: historyMessage, user: currentUser.name, timestamp: new Date() };
            const updatedHistory = [...transaction.history, newHistoryEntry];
            await updateDoc(docRef, { workflowState: newState, history: updatedHistory });
            if (newState === 'completed') {
                await addDoc(collection(db, `artifacts/${appId}/public/data/documents`), { type: 'correspondence', correspondenceType: 'صادر', subject: `[مؤرشف] ${transaction.subject}`, party: transaction.party, referenceNumber: transaction.referenceNumber, date: new Date().toISOString().split('T')[0], createdAt: serverTimestamp() });
            }
        }
        async function handleMoveItem(itemId, newParentId) {
            const docRef = doc(db, `artifacts/${appId}/public/data/documents`, itemId);
            await updateDoc(docRef, { parentId: newParentId === 'null' ? null : newParentId });
        }
        
        document.body.addEventListener('click', (e) => {
            // Close options menu if clicking outside
            if (!e.target.closest('.folder-options-btn') && !e.target.closest('#folder-options-menu')) {
                closeOptionsMenu();
            }

            const folderLink = e.target.closest('.folder-link');
            const deleteBtn = e.target.closest('.delete-item-btn');
            const itemCard = e.target.closest('.item-card');
            const toggle = e.target.closest('.tree-toggle');
            const optionsBtn = e.target.closest('.folder-options-btn');

            if (folderLink) { e.preventDefault(); currentFolderId = folderLink.dataset.id === 'null' ? null : folderLink.dataset.id; currentView = 'archive'; render(); renderArchiveTree(); }
            if (deleteBtn) { 
                e.preventDefault(); 
                e.stopPropagation(); 
                const idToDelete = deleteBtn.dataset.id; 
                // confirm() is removed as it's not supported. A custom modal is recommended.
                deleteDoc(doc(db, `artifacts/${appId}/public/data/documents`, idToDelete)); 
            }
            if(itemCard) { e.preventDefault(); openDetailModal(itemCard.dataset.id); }
            if(toggle) { e.preventDefault(); const id = toggle.dataset.id; expandedFolders[id] = !expandedFolders[id]; renderArchiveTree(); }
            if(optionsBtn) { e.preventDefault(); e.stopPropagation(); openFolderOptionsMenu(optionsBtn.dataset.id, optionsBtn); }
        });

        function openFolderOptionsMenu(folderId, button) {
            closeOptionsMenu(); // Close any existing menu
            const rect = button.getBoundingClientRect();
            const menu = document.createElement('div');
            menu.id = 'folder-options-menu';
            menu.className = 'absolute z-10 bg-white rounded-md shadow-lg border text-sm';
            menu.style.top = `${rect.bottom}px`;
            menu.style.left = `${rect.left - 100}px`;
            menu.innerHTML = `
                <a href="#" data-action="rename" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">إعادة تسمية</a>
                <a href="#" data-action="new_folder" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">مجلد جديد بالداخل</a>
                <hr>
                <a href="#" data-action="delete" class="block px-4 py-2 text-red-600 hover:bg-gray-100">حذف</a>
            `;
            document.body.appendChild(menu);
            menu.addEventListener('click', (e) => {
                e.preventDefault();
                const action = e.target.dataset.action;
                if(action === 'rename') { openRenameFolderModal(folderId); }
                if(action === 'new_folder') { openNewSubFolderModal(folderId); }
                if(action === 'delete') { 
                    // confirm() is removed.
                    deleteDoc(doc(db, `artifacts/${appId}/public/data/documents`, folderId)); 
                }
                closeOptionsMenu();
            });
        }
        function closeOptionsMenu() {
            const menu = document.getElementById('folder-options-menu');
            if(menu) menu.remove();
        }

        function openRenameFolderModal(folderId) {
            const folder = allDocuments.find(f => f.id === folderId);
            const renderFn = () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-md transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">إعادة تسمية المجلد</h3><button class="close-modal-btn text-gray-500">&times;</button></div><form id="rename-folder-form" data-folder-id="${folderId}" class="p-6 space-y-4"><div><label for="new-folder-name" class="block text-sm font-medium text-gray-700">الاسم الجديد</label><input type="text" id="new-folder-name" value="${folder.name}" required class="mt-1 block w-full input-field"></div><div class="flex justify-end pt-4"><button type="submit" class="bg-sky-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-sky-700">حفظ</button></div></form></div>`.replaceAll('input-field', 'px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500');
            const { open } = setupModal('rename-folder-modal', null, 'rename-folder-form', handleRenameFolder, renderFn);
            open();
        }

        function openNewSubFolderModal(parentId) {
            const { open } = addFolderModal;
            document.getElementById('add-folder-form').dataset.parentId = parentId;
            open();
        }

        function openDetailModal(docId) {
            const item = allDocuments.find(d => d.id === docId);
            if (!item) return;
            const modalContainer = document.getElementById('detail-modal');
            let modalHtml = '';
            if(item.type === 'transaction') {
                let actionsHtml = '';
                switch(item.workflowState) {
                    case 'new': actionsHtml = `<button data-action="start_progress" class="action-btn bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">بدء الإجراء</button>`; break;
                    case 'in_progress': actionsHtml = `<button data-action="send_for_approval" class="action-btn bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">إرسال للاعتماد</button>`; break;
                    case 'pending_approval': actionsHtml = `<button data-action="approve" class="action-btn bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">اعتماد نهائي وأرشفة</button>`; break;
                }
                modalHtml = `<div class="p-6"><div class="grid grid-cols-3 gap-4 mb-6 text-sm"><div><span class="font-bold">الجهة:</span> ${item.party}</div><div><span class="font-bold">رقم الإشارة:</span> ${item.referenceNumber || ''}</div><div><span class="font-bold">الحالة:</span> <span class="font-semibold p-1 rounded ${workflowStates[item.workflowState].color}">${workflowStates[item.workflowState].title}</span></div></div><h4 class="font-bold mb-2">سجل الإجراءات:</h4><ul class="border rounded-md p-2 h-48 overflow-y-auto bg-gray-50 text-sm">${item.history.map(h => `<li class="p-2 border-b"><strong>${h.action}</strong> <span class="text-gray-500">بواسطة ${h.user} في ${h.timestamp.toLocaleString('ar-EG')}</span></li>`).join('')}</ul><div class="flex justify-end pt-6 gap-2"><button data-id="${item.id}" class="move-item-btn bg-gray-500 text-white font-bold py-2 px-4 rounded">نقل إلى مجلد</button>${actionsHtml}<button class="close-modal-btn bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded">إغلاق</button></div></div>`;
            } else {
                 modalHtml = `<div class="p-6"><div class="grid grid-cols-2 gap-4 mb-6 text-sm"><div><span class="font-bold">النوع:</span> ${item.correspondenceType}</div><div><span class="font-bold">التاريخ:</span> ${item.date}</div><div><span class="font-bold">الجهة:</span> ${item.party}</div><div><span class="font-bold">رقم الإشارة:</span> ${item.referenceNumber || ''}</div></div><div class="flex justify-end pt-6"><button data-id="${item.id}" class="move-item-btn bg-gray-500 text-white font-bold py-2 px-4 rounded">نقل إلى مجلد</button><button class="close-modal-btn bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded">إغلاق</button></div></div>`;
            }
            const renderFn = () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-3xl transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">${item.subject || item.name}</h3><button class="close-modal-btn text-gray-500">&times;</button></div>${modalHtml}</div>`;
            const { open, close } = setupModal('detail-modal', null, null, null, renderFn);
            modalContainer.querySelector('.action-btn')?.addEventListener('click', (e) => { handleTakeAction(docId, e.target.dataset.action); close(); });
            modalContainer.querySelector('.move-item-btn')?.addEventListener('click', (e) => { openMoveItemModal(e.target.dataset.id); close(); });
            open();
        }

        function openMoveItemModal(itemId) {
            const folders = allDocuments.filter(d => d.type === 'folder');
            const buildOptions = (parentId, prefix) => {
                let html = '';
                const children = folders.filter(f => f.parentId === parentId);
                children.forEach(folder => {
                    html += `<option value="${folder.id}">${prefix}${folder.name}</option>`;
                    html += buildOptions(folder.id, prefix + '-- ');
                });
                return html;
            };
            const renderFn = () => `<div class="modal-content bg-white rounded-xl shadow-2xl w-full max-w-md transform -translate-y-10 scale-95"><div class="p-4 border-b flex justify-between items-center"><h3 class="text-xl font-bold">نقل إلى مجلد</h3><button class="close-modal-btn text-gray-500">&times;</button></div><form id="move-item-form" data-item-id="${itemId}" class="p-6 space-y-4"><div><label for="destination-folder" class="block text-sm font-medium text-gray-700">اختر المجلد الوجهة</label><select id="destination-folder" class="mt-1 block w-full input-field"><option value="null">المجلد الرئيسي</option>${buildOptions(null, '')}</select></div><div class="flex justify-end pt-4"><button type="submit" class="bg-sky-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-sky-700">نقل</button></div></form></div>`.replaceAll('input-field', 'px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500');
            const { open } = setupModal('move-item-modal', null, 'move-item-form', (form) => {
                const newParentId = form.querySelector('#destination-folder').value;
                handleMoveItem(itemId, newParentId);
            }, renderFn);
            open();
        }

        document.getElementById('nav-dashboard').addEventListener('click', (e) => { e.preventDefault(); currentView = 'dashboard'; render(); });
        document.getElementById('nav-incoming').addEventListener('click', (e) => { e.preventDefault(); currentView = 'incoming'; render(); });
        document.getElementById('nav-outgoing').addEventListener('click', (e) => { e.preventDefault(); currentView = 'outgoing'; render(); });
        
        async function initializeAppWithAuth() {
            try {
                await (typeof __initial_auth_token !== 'undefined' ? signInWithCustomToken(auth, __initial_auth_token) : signInAnonymously(auth));
                const docsQuery = query(collection(db, `artifacts/${appId}/public/data/documents`), orderBy('createdAt', 'desc'));
                onSnapshot(docsQuery, (snapshot) => {
                    allDocuments = snapshot.docs.map(doc => {
                        const data = doc.data();
                        const convertedData = { ...data };
                        if (data.createdAt?.toDate) convertedData.createdAt = data.createdAt.toDate();
                        if (data.history) { convertedData.history = data.history.map(h => ({ ...h, timestamp: h.timestamp?.toDate ? h.timestamp.toDate() : new Date() })); }
                        return { id: doc.id, ...convertedData };
                    });
                    render();
                    renderArchiveTree(); // Initial render
                    loadingOverlay.classList.add('opacity-0', 'pointer-events-none');
                });
            } catch (error) {
                console.error("Authentication or Firestore setup failed:", error);
                loadingOverlay.innerHTML = "<p>فشل تحميل البيانات. يرجى تحديث الصفحة.</p>";
            }
        }
        initializeAppWithAuth();
    </script>
</body>
</html>
